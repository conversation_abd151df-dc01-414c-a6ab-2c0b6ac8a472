"""
Modulo per la generazione di percorsi di lavorazione CAM.

Supporta:
- Generazione percorsi per diverse lavorazioni
- Integrazione con geometrie OCC
- Calcolo automatico di percorsi ottimizzati
- Gestione di facce e superfici complesse
"""

from typing import List, Tuple, Dict, Optional, Any
import numpy as np

# Import OCC opzionali
try:
    from OCC.Core.TopoDS import TopoDS_Face, TopoDS_Shape, TopoDS_Edge
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE, TopAbs_EDGE
    from OCC.Core.BRep import BRep_Tool
    from OCC.Core.GeomAPI import GeomAPI_ProjectPointOnSurf
    from OCC.Core.gp import gp_Pnt, gp_Vec, gp_Dir
    from OCC.Core.Geom import Geom_Surface
    OCC_AVAILABLE = True
except ImportError:
    OCC_AVAILABLE = False
    # Classi placeholder
    class TopoDS_Face:
        pass
    class TopoDS_Shape:
        pass
    class TopoDS_Edge:
        pass

from .operations import Operation, OperationType, RoughingOperation, ContouringOperation, EngravingOperation
from .coordinate_system import CoordinateSystem

# Import tool_lib con gestione errori
try:
    from .tool_lib.tool_lib.base import Tool
except ImportError:
    try:
        from tool_lib.tool_lib.base import Tool
    except ImportError:
        # Classe placeholder se tool_lib non disponibile
        class Tool:
            def __init__(self, name, diameter=None, length=None, code=None):
                self.name = name
                self.diameter = diameter
                self.length = length
                self.code = code


class PathPoint:
    """Rappresenta un punto nel percorso di lavorazione."""

    def __init__(self, x: float, y: float, z: float, feed_rate: Optional[float] = None,
                 spindle_speed: Optional[int] = None, is_rapid: bool = False):
        """
        Inizializza un punto del percorso.

        Args:
            x, y, z: Coordinate del punto
            feed_rate: Velocità di avanzamento specifica per questo punto
            spindle_speed: Velocità mandrino specifica per questo punto
            is_rapid: Se True, movimento rapido (G0)
        """
        self.x = x
        self.y = y
        self.z = z
        self.feed_rate = feed_rate
        self.spindle_speed = spindle_speed
        self.is_rapid = is_rapid

    def to_tuple(self) -> Tuple[float, float, float]:
        """Restituisce le coordinate come tupla."""
        return (self.x, self.y, self.z)

    def distance_to(self, other: 'PathPoint') -> float:
        """Calcola la distanza da un altro punto."""
        return np.sqrt((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)


class ToolPath:
    """Rappresenta un percorso completo per un utensile."""

    def __init__(self, operation: Operation, tool: Tool):
        """
        Inizializza un percorso utensile.

        Args:
            operation: Lavorazione associata
            tool: Utensile utilizzato
        """
        self.operation = operation
        self.tool = tool
        self.points: List[PathPoint] = []
        self.segments: List[List[PathPoint]] = []
        self.total_length = 0.0
        self.machining_time = 0.0

    def add_point(self, point: PathPoint):
        """Aggiunge un punto al percorso."""
        self.points.append(point)

    def add_segment(self, segment: List[PathPoint]):
        """Aggiunge un segmento al percorso."""
        self.segments.append(segment)
        self.points.extend(segment)

    def calculate_length(self) -> float:
        """Calcola la lunghezza totale del percorso."""
        total = 0.0
        for i in range(1, len(self.points)):
            total += self.points[i-1].distance_to(self.points[i])
        self.total_length = total
        return total

    def calculate_time(self) -> float:
        """Calcola il tempo di lavorazione stimato."""
        if not self.points:
            return 0.0

        total_time = 0.0
        for i in range(1, len(self.points)):
            distance = self.points[i-1].distance_to(self.points[i])
            feed_rate = self.points[i].feed_rate or self.operation.feed_rate
            if not self.points[i].is_rapid and feed_rate > 0:
                total_time += distance / feed_rate  # tempo in minuti

        self.machining_time = total_time
        return total_time


class PathGenerator:
    """Generatore principale di percorsi CAM."""

    def __init__(self, coordinate_system: Optional[CoordinateSystem] = None):
        """
        Inizializza il generatore di percorsi.

        Args:
            coordinate_system: Sistema di coordinate da utilizzare
        """
        self.coordinate_system = coordinate_system
        self.tolerance = 0.01  # mm
        self.optimization_enabled = True

    def generate_path(self, operation: Operation) -> ToolPath:
        """
        Genera il percorso per una lavorazione.

        Args:
            operation: Lavorazione da eseguire

        Returns:
            Percorso generato
        """
        if not operation.tool:
            raise ValueError("Nessun utensile specificato per la lavorazione")

        if not operation.faces:
            raise ValueError("Nessuna faccia specificata per la lavorazione")

        # Valida la lavorazione
        is_valid, errors = operation.validate()
        if not is_valid:
            raise ValueError(f"Lavorazione non valida: {', '.join(errors)}")

        # Genera il percorso in base al tipo di lavorazione
        if operation.operation_type == OperationType.ROUGHING:
            return self._generate_roughing_path(operation)
        elif operation.operation_type == OperationType.CONTOURING:
            return self._generate_contouring_path(operation)
        elif operation.operation_type == OperationType.ENGRAVING:
            return self._generate_engraving_path(operation)
        else:
            raise ValueError(f"Tipo di lavorazione non supportato: {operation.operation_type}")

    def _generate_roughing_path(self, operation: RoughingOperation) -> ToolPath:
        """Genera percorso di sgrossatura."""
        tool_path = ToolPath(operation, operation.tool)

        for face in operation.faces:
            # Ottieni i limiti della faccia
            bounds = self._get_face_bounds(face)
            if not bounds:
                continue

            # Genera pattern di sgrossatura
            if operation.roughing_strategy == "zigzag":
                segments = self._generate_zigzag_pattern(bounds, operation)
            elif operation.roughing_strategy == "spiral":
                segments = self._generate_spiral_pattern(bounds, operation)
            else:
                segments = self._generate_parallel_pattern(bounds, operation)

            # Proietta i punti sulla superficie
            for segment in segments:
                projected_segment = self._project_points_on_face(segment, face, operation)
                if projected_segment:
                    tool_path.add_segment(projected_segment)

        return tool_path

    def _generate_contouring_path(self, operation: ContouringOperation) -> ToolPath:
        """Genera percorso di contornatura."""
        tool_path = ToolPath(operation, operation.tool)

        for face in operation.faces:
            # Estrai i bordi della faccia
            edges = self._get_face_edges(face)

            for edge in edges:
                # Genera punti lungo il bordo
                edge_points = self._discretize_edge(edge, operation.tolerance)

                # Aggiungi lead-in e lead-out
                if operation.lead_in_distance > 0:
                    lead_in = self._generate_lead_in(edge_points[0], operation.lead_in_distance)
                    edge_points = lead_in + edge_points

                if operation.lead_out_distance > 0:
                    lead_out = self._generate_lead_out(edge_points[-1], operation.lead_out_distance)
                    edge_points.extend(lead_out)

                # Ripeti per il numero di passate di finitura
                for pass_num in range(operation.finish_passes + operation.spring_passes):
                    is_spring_pass = pass_num >= operation.finish_passes
                    segment_points = []

                    for point in edge_points:
                        path_point = PathPoint(
                            point[0], point[1], point[2],
                            feed_rate=0 if is_spring_pass else operation.feed_rate
                        )
                        segment_points.append(path_point)

                    tool_path.add_segment(segment_points)

        return tool_path

    def _generate_engraving_path(self, operation: EngravingOperation) -> ToolPath:
        """Genera percorso di incisione."""
        tool_path = ToolPath(operation, operation.tool)

        if operation.text_content:
            # Genera percorso per testo
            text_paths = self._generate_text_paths(operation)
            for path in text_paths:
                tool_path.add_segment(path)
        else:
            # Genera percorso per facce selezionate
            for face in operation.faces:
                face_path = self._generate_face_engraving(face, operation)
                if face_path:
                    tool_path.add_segment(face_path)

        return tool_path

    def _get_face_bounds(self, face: TopoDS_Face) -> Optional[Tuple[float, float, float, float]]:
        """Calcola i limiti di una faccia (xmin, ymin, xmax, ymax)."""
        try:
            from OCC.Core.Bnd import Bnd_Box
            from OCC.Core.BRepBndLib import brepbndlib_Add

            bbox = Bnd_Box()
            brepbndlib_Add(face, bbox)
            xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()
            return (xmin, ymin, xmax, ymax)
        except:
            return None

    def _get_face_edges(self, face: TopoDS_Face) -> List[TopoDS_Edge]:
        """Estrae i bordi di una faccia."""
        edges = []
        explorer = TopExp_Explorer(face, TopAbs_EDGE)
        while explorer.More():
            edge = explorer.Current()
            edges.append(edge)
            explorer.Next()
        return edges

    def _discretize_edge(self, edge: TopoDS_Edge, tolerance: float) -> List[Tuple[float, float, float]]:
        """Discretizza un bordo in punti."""
        points = []
        try:
            from OCC.Core.BRepAdaptor import BRepAdaptor_Curve
            from OCC.Core.GCPnts import GCPnts_UniformAbscissa

            curve_adaptor = BRepAdaptor_Curve(edge)
            discretizer = GCPnts_UniformAbscissa(curve_adaptor, tolerance)

            if discretizer.IsDone():
                for i in range(1, discretizer.NbPoints() + 1):
                    param = discretizer.Parameter(i)
                    point = curve_adaptor.Value(param)
                    points.append((point.X(), point.Y(), point.Z()))
        except:
            pass

        return points

    def _generate_zigzag_pattern(self, bounds: Tuple[float, float, float, float],
                                operation: RoughingOperation) -> List[List[Tuple[float, float, float]]]:
        """Genera pattern a zigzag per sgrossatura."""
        xmin, ymin, xmax, ymax = bounds
        step_over = operation.step_over

        segments = []
        y = ymin
        direction = 1

        while y <= ymax:
            if direction > 0:
                segment = [(xmin, y, 0), (xmax, y, 0)]
            else:
                segment = [(xmax, y, 0), (xmin, y, 0)]

            segments.append(segment)
            y += step_over
            direction *= -1

        return segments

    def _generate_spiral_pattern(self, bounds: Tuple[float, float, float, float],
                                operation: RoughingOperation) -> List[List[Tuple[float, float, float]]]:
        """Genera pattern a spirale per sgrossatura."""
        # Implementazione semplificata
        return self._generate_parallel_pattern(bounds, operation)

    def _generate_parallel_pattern(self, bounds: Tuple[float, float, float, float],
                                  operation: RoughingOperation) -> List[List[Tuple[float, float, float]]]:
        """Genera pattern parallelo per sgrossatura."""
        xmin, ymin, xmax, ymax = bounds
        step_over = operation.step_over

        segments = []
        y = ymin

        while y <= ymax:
            segment = [(xmin, y, 0), (xmax, y, 0)]
            segments.append(segment)
            y += step_over

        return segments

    def _project_points_on_face(self, points: List[Tuple[float, float, float]],
                               face: TopoDS_Face, operation: Operation) -> List[PathPoint]:
        """Proietta punti su una faccia."""
        projected_points = []

        try:
            surface = BRep_Tool.Surface(face)

            for point in points:
                gp_point = gp_Pnt(point[0], point[1], point[2])
                projector = GeomAPI_ProjectPointOnSurf(gp_point, surface)

                if projector.NbPoints() > 0:
                    projected = projector.Point(1)
                    # Sottrai la profondità di lavorazione
                    z_offset = operation.step_down if hasattr(operation, 'step_down') else 0.1
                    path_point = PathPoint(
                        projected.X(),
                        projected.Y(),
                        projected.Z() - z_offset,
                        feed_rate=operation.feed_rate
                    )
                    projected_points.append(path_point)
        except:
            # Fallback: usa i punti originali
            for point in points:
                path_point = PathPoint(point[0], point[1], point[2], feed_rate=operation.feed_rate)
                projected_points.append(path_point)

        return projected_points

    def _generate_lead_in(self, start_point: Tuple[float, float, float],
                         distance: float) -> List[Tuple[float, float, float]]:
        """Genera punti di entrata graduale."""
        # Implementazione semplificata: movimento lineare
        x, y, z = start_point
        return [(x - distance, y, z + distance), start_point]

    def _generate_lead_out(self, end_point: Tuple[float, float, float],
                          distance: float) -> List[Tuple[float, float, float]]:
        """Genera punti di uscita graduale."""
        # Implementazione semplificata: movimento lineare
        x, y, z = end_point
        return [end_point, (x + distance, y, z + distance)]

    def _generate_text_paths(self, operation: EngravingOperation) -> List[List[PathPoint]]:
        """Genera percorsi per incisione di testo."""
        # Placeholder - integrazione con font processor
        paths = []

        # TODO: Integrare con il font processor esistente
        # per ora restituisce un percorso semplice
        if operation.text_content:
            simple_path = [
                PathPoint(0, 0, -operation.engraving_depth, feed_rate=operation.feed_rate),
                PathPoint(operation.font_size, 0, -operation.engraving_depth, feed_rate=operation.feed_rate)
            ]
            paths.append(simple_path)

        return paths

    def _generate_face_engraving(self, face: TopoDS_Face,
                                operation: EngravingOperation) -> Optional[List[PathPoint]]:
        """Genera percorso di incisione per una faccia."""
        # Implementazione semplificata
        bounds = self._get_face_bounds(face)
        if not bounds:
            return None

        xmin, ymin, xmax, ymax = bounds
        path = [
            PathPoint(xmin, ymin, -operation.engraving_depth, feed_rate=operation.feed_rate),
            PathPoint(xmax, ymax, -operation.engraving_depth, feed_rate=operation.feed_rate)
        ]

        return path
