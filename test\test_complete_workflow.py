"""
Test script completo per il workflow CAM con visualizzazione PyVista.

Questo script testa l'intero workflow:
1. Creazione grezzo
2. Definizione sistema di coordinate
3. Creazione operazioni
4. Generazione percorsi
5. Ottimizzazione
6. Visualizzazione completa
"""

import sys
import os
# Aggiungi il percorso della libreria principale
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pyvista as pv
import numpy as np

# Import libreria CAM
from blank import RectangularBlank, CylindricalBlank
from coordinate_system import CoordinateSystem
from operations import (
    OperationType, RoughingOperation, ContouringOperation, EngravingOperation,
    MathematicalSurface
)
from path_generator import PathGenerator
from optimization import PathOptimizer, OptimizationSettings
from setup import CAMSetup


# Classe placeholder per Tool se non disponibile
class Tool:
    def __init__(self, name, diameter, length, flutes=2, mill_type="flat", code=None):
        self.name = name
        self.diameter = diameter
        self.length = length
        self.flutes = flutes
        self.mill_type = mill_type
        self.code = code


def create_surface_mesh(surface):
    """Crea una mesh PyVista da una superficie matematica."""
    if surface.surface_type == 'plane':
        center = surface.parameters.get('center', (0, 0, 0))
        size = surface.parameters.get('size', (10, 10))
        
        plane = pv.Plane(
            center=center,
            direction=(0, 0, 1),
            i_size=size[0],
            j_size=size[1]
        )
        return plane
        
    elif surface.surface_type == 'cylinder':
        center = surface.parameters.get('center', (0, 0, 0))
        radius = surface.parameters.get('radius', 1)
        height = surface.parameters.get('height', 1)
        
        cylinder = pv.Cylinder(
            center=(center[0], center[1], center[2] + height/2),
            direction=(0, 0, 1),
            radius=radius,
            height=height
        )
        return cylinder
        
    else:
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox
        
        box = pv.Box(bounds=[
            min_point[0], max_point[0],
            min_point[1], max_point[1], 
            min_point[2], max_point[2]
        ])
        return box


def create_blank_mesh(blank):
    """Crea una mesh PyVista da un grezzo."""
    if hasattr(blank, 'diameter'):  # Cilindrico
        return pv.Cylinder(
            center=(blank.center[0], blank.center[1], blank.center[2] + blank.height/2),
            direction=(0, 0, 1),
            radius=blank.radius,
            height=blank.height
        )
    else:  # Rettangolare
        return pv.Box(bounds=[
            blank.corner[0], blank.corner[0] + blank.length,
            blank.corner[1], blank.corner[1] + blank.width,
            blank.corner[2], blank.corner[2] + blank.height
        ])


def visualize_tool_path(plotter, tool_path, color='red', line_width=2, label='Tool Path'):
    """Visualizza un percorso utensile in PyVista."""
    if not tool_path or not tool_path.points:
        return
    
    points = np.array([[p.x, p.y, p.z] for p in tool_path.points])
    
    if len(points) > 1:
        spline = pv.Spline(points)
        plotter.add_mesh(spline, color=color, line_width=line_width, label=label)
        
        # Punti inizio/fine
        plotter.add_points(points[0:1], color='green', point_size=8)
        plotter.add_points(points[-1:], color='red', point_size=8)


def test_rectangular_part_workflow():
    """Test workflow completo per pezzo rettangolare."""
    print("=== Test Workflow Pezzo Rettangolare ===")
    
    # 1. Crea setup
    setup = CAMSetup("Pezzo_Rettangolare")
    
    # 2. Crea grezzo
    blank = RectangularBlank(
        name="Grezzo_Alluminio",
        length=80.0,
        width=60.0,
        height=25.0,
        corner=(-5, -5, 0),
        material="Aluminum"
    )
    setup.set_blank(blank)
    
    # 3. Sistema di coordinate
    coord_system = CoordinateSystem("Fresatura")
    coord_system.set_origin(35, 25, 25)  # Centro superficie superiore
    setup.set_coordinate_system(coord_system, work_offset_number=1)
    
    # 4. Superfici del modello
    top_surface = MathematicalSurface('plane', {
        'center': (35, 25, 20),
        'size': (60, 40),
        'normal': (0, 0, 1)
    })
    setup.add_model_surface(top_surface)
    
    # 5. Utensili
    roughing_tool = Tool("Fresa_Sgrossatura_8mm", diameter=8.0, length=50.0, code="T1")
    finishing_tool = Tool("Fresa_Finitura_4mm", diameter=4.0, length=40.0, code="T2")
    
    # 6. Operazioni
    # Sgrossatura
    roughing_op = setup.create_operation(
        OperationType.ROUGHING,
        "Sgrossatura",
        roughing_tool,
        [top_surface]
    )
    roughing_op.set_roughing_parameters(stock_to_leave=0.5, max_depth=2.0, strategy="zigzag")
    roughing_op.set_speeds_and_feeds(2500, 400, 100)
    roughing_op.set_precision(tolerance=0.1, step_down=2.0, step_over=6.0)
    
    # Finitura
    finishing_op = setup.create_operation(
        OperationType.CONTOURING,
        "Finitura",
        finishing_tool,
        [top_surface]
    )
    finishing_op.set_contouring_parameters(finish_passes=2, spring_passes=1)
    finishing_op.set_speeds_and_feeds(3500, 250, 75)
    finishing_op.set_precision(tolerance=0.02, step_down=0.5)
    
    # 7. Valida setup
    is_valid, errors = setup.validate_setup()
    print(f"Setup valido: {is_valid}")
    if not is_valid:
        print(f"Errori: {errors}")
        return None
    
    # 8. Genera percorsi
    print("Generazione percorsi...")
    tool_paths = setup.generate_all_paths(enable_optimization=True)
    
    # 9. Statistiche
    setup_info = setup.get_setup_info()
    print(f"Operazioni completate: {len(tool_paths)}")
    print(f"Tempo totale stimato: {setup_info['total_machining_time']:.2f} minuti")
    
    # 10. Visualizzazione
    plotter = pv.Plotter(title="Workflow Pezzo Rettangolare")
    
    # Grezzo
    blank_mesh = create_blank_mesh(blank)
    plotter.add_mesh(blank_mesh, color='lightblue', opacity=0.3, label='Grezzo')
    
    # Superficie da lavorare
    surface_mesh = create_surface_mesh(top_surface)
    plotter.add_mesh(surface_mesh, color='lightgreen', opacity=0.6, label='Superficie Lavorazione')
    
    # Percorsi
    colors = ['red', 'blue', 'purple']
    for i, tool_path in enumerate(tool_paths):
        if tool_path:
            color = colors[i % len(colors)]
            label = f"{tool_path.operation.name}"
            visualize_tool_path(plotter, tool_path, color=color, label=label)
    
    # Sistema di coordinate
    origin = coord_system.origin
    axis_length = 20
    
    # Assi X, Y, Z
    x_line = pv.Line([origin[0], origin[1], origin[2]], 
                     [origin[0] + axis_length, origin[1], origin[2]])
    y_line = pv.Line([origin[0], origin[1], origin[2]], 
                     [origin[0], origin[1] + axis_length, origin[2]])
    z_line = pv.Line([origin[0], origin[1], origin[2]], 
                     [origin[0], origin[1], origin[2] + axis_length])
    
    plotter.add_mesh(x_line, color='red', line_width=3, label='X Axis')
    plotter.add_mesh(y_line, color='green', line_width=3, label='Y Axis')
    plotter.add_mesh(z_line, color='blue', line_width=3, label='Z Axis')
    
    # Informazioni
    info_text = f"""Setup: {setup.name}
Grezzo: {blank.name} ({blank.material})
Dimensioni: {blank.length}x{blank.width}x{blank.height} mm
Operazioni: {len(setup.operations)}
Percorsi generati: {len(tool_paths)}
Tempo totale: {setup_info['total_machining_time']:.2f} min
Work Offset: G54"""
    
    plotter.add_text(info_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return setup


def test_cylindrical_part_workflow():
    """Test workflow completo per pezzo cilindrico."""
    print("\n=== Test Workflow Pezzo Cilindrico ===")
    
    # 1. Crea setup
    setup = CAMSetup("Pezzo_Cilindrico")
    
    # 2. Crea grezzo cilindrico
    blank = CylindricalBlank(
        name="Cilindro_Acciaio",
        diameter=50.0,
        height=80.0,
        center=(0, 0, 0),
        material="Steel"
    )
    setup.set_blank(blank)
    
    # 3. Sistema di coordinate
    coord_system = CoordinateSystem("Tornitura")
    coord_system.set_origin(0, 0, 40)  # Centro altezza
    setup.set_coordinate_system(coord_system, work_offset_number=2)  # G55
    
    # 4. Superfici del modello
    cylinder_surface = MathematicalSurface('cylinder', {
        'center': (0, 0, 10),
        'radius': 20,
        'height': 60
    })
    setup.add_model_surface(cylinder_surface)
    
    # 5. Utensile per incisione
    engraving_tool = Tool("Fresa_Incisione_2mm", diameter=2.0, length=40.0, code="T1")
    
    # 6. Operazione di incisione
    engraving_op = setup.create_operation(
        OperationType.ENGRAVING,
        "Incisione_Cilindro",
        engraving_tool,
        [cylinder_surface]
    )
    engraving_op.set_engraving_parameters(depth=0.3, text="CAM LIB", font_size=10.0)
    engraving_op.set_speeds_and_feeds(4000, 150, 50)
    engraving_op.set_precision(tolerance=0.01)
    
    # 7. Genera percorsi
    print("Generazione percorsi...")
    tool_paths = setup.generate_all_paths(enable_optimization=True)
    
    # 8. Visualizzazione
    plotter = pv.Plotter(title="Workflow Pezzo Cilindrico")
    
    # Grezzo
    blank_mesh = create_blank_mesh(blank)
    plotter.add_mesh(blank_mesh, color='lightcoral', opacity=0.3, label='Grezzo Cilindrico')
    
    # Superficie da incidere
    surface_mesh = create_surface_mesh(cylinder_surface)
    plotter.add_mesh(surface_mesh, color='lightyellow', opacity=0.6, label='Superficie Incisione')
    
    # Percorso incisione
    if tool_paths:
        visualize_tool_path(plotter, tool_paths[0], color='purple', label='Percorso Incisione')
    
    # Sistema di coordinate
    origin = coord_system.origin
    axis_length = 15
    
    x_line = pv.Line([origin[0], origin[1], origin[2]], 
                     [origin[0] + axis_length, origin[1], origin[2]])
    y_line = pv.Line([origin[0], origin[1], origin[2]], 
                     [origin[0], origin[1] + axis_length, origin[2]])
    z_line = pv.Line([origin[0], origin[1], origin[2]], 
                     [origin[0], origin[1], origin[2] + axis_length])
    
    plotter.add_mesh(x_line, color='red', line_width=3)
    plotter.add_mesh(y_line, color='green', line_width=3)
    plotter.add_mesh(z_line, color='blue', line_width=3)
    
    # Informazioni
    setup_info = setup.get_setup_info()
    info_text = f"""Setup: {setup.name}
Grezzo: {blank.name} ({blank.material})
Diametro: Ø{blank.diameter} mm
Altezza: {blank.height} mm
Operazioni: {len(setup.operations)}
Tempo totale: {setup_info['total_machining_time']:.2f} min
Work Offset: G55"""
    
    plotter.add_text(info_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return setup


def main():
    """Esegue tutti i test del workflow completo."""
    print("Test Workflow Completo CAM con Visualizzazione PyVista")
    print("=" * 60)
    
    try:
        # Test 1: Workflow pezzo rettangolare
        rect_setup = test_rectangular_part_workflow()
        
        # Test 2: Workflow pezzo cilindrico
        cyl_setup = test_cylindrical_part_workflow()
        
        print("\n" + "=" * 60)
        print("Tutti i test del workflow completati con successo!")
        
        # Riepilogo finale
        if rect_setup and cyl_setup:
            print("\nRiepilogo finale:")
            print(f"Setup testati: 2")
            print(f"Operazioni totali: {len(rect_setup.operations) + len(cyl_setup.operations)}")
            print(f"Percorsi generati: {len(rect_setup.tool_paths) + len(cyl_setup.tool_paths)}")
            
            rect_info = rect_setup.get_setup_info()
            cyl_info = cyl_setup.get_setup_info()
            total_time = rect_info['total_machining_time'] + cyl_info['total_machining_time']
            print(f"Tempo totale stimato: {total_time:.2f} minuti")
            
    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
