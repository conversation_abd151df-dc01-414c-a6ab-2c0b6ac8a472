"""
Script principale per eseguire tutti i test della libreria CAM.

Questo script esegue tutti i test in sequenza e fornisce un riepilogo finale.
"""

import sys
import os
import subprocess
import time

def run_test(test_name, test_file):
    """Esegue un singolo test e restituisce il risultato."""
    print(f"\n{'='*60}")
    print(f"ESECUZIONE TEST: {test_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Esegui il test
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, 
                              text=True, 
                              cwd=os.path.dirname(test_file))
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {test_name} COMPLETATO CON SUCCESSO")
            print(f"⏱️  Tempo di esecuzione: {duration:.2f} secondi")
            if result.stdout:
                print("\nOutput:")
                print(result.stdout[-500:])  # Ultimi 500 caratteri
            return True, duration, None
        else:
            print(f"❌ {test_name} FALLITO")
            print(f"⏱️  Tempo di esecuzione: {duration:.2f} secondi")
            print("\nErrore:")
            print(result.stderr)
            return False, duration, result.stderr
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ {test_name} ERRORE DURANTE L'ESECUZIONE")
        print(f"⏱️  Tempo di esecuzione: {duration:.2f} secondi")
        print(f"Errore: {e}")
        return False, duration, str(e)


def main():
    """Esegue tutti i test."""
    print("🚀 AVVIO TEST SUITE LIBRERIA CAM")
    print("=" * 60)
    
    # Directory dei test
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Lista dei test da eseguire
    tests = [
        ("Test Geometria Grezzi", os.path.join(test_dir, "test_blank_geometry.py")),
        ("Test Sistemi di Coordinate", os.path.join(test_dir, "test_coordinate_systems.py")),
        ("Test Operazioni CAM", os.path.join(test_dir, "test_operations.py")),
        ("Test Generazione Percorsi", os.path.join(test_dir, "test_path_generation.py")),
        ("Test Ottimizzazione", os.path.join(test_dir, "test_optimization.py")),
        ("Test Workflow Completo", os.path.join(test_dir, "test_complete_workflow.py"))
    ]
    
    # Risultati
    results = []
    total_start_time = time.time()
    
    # Esegui tutti i test
    for test_name, test_file in tests:
        if os.path.exists(test_file):
            success, duration, error = run_test(test_name, test_file)
            results.append((test_name, success, duration, error))
        else:
            print(f"⚠️  File di test non trovato: {test_file}")
            results.append((test_name, False, 0, "File non trovato"))
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # Riepilogo finale
    print(f"\n{'='*60}")
    print("📊 RIEPILOGO FINALE TEST SUITE")
    print(f"{'='*60}")
    
    successful_tests = sum(1 for _, success, _, _ in results if success)
    total_tests = len(results)
    
    print(f"📈 Test completati: {total_tests}")
    print(f"✅ Test riusciti: {successful_tests}")
    print(f"❌ Test falliti: {total_tests - successful_tests}")
    print(f"⏱️  Tempo totale: {total_duration:.2f} secondi")
    print(f"📊 Percentuale successo: {(successful_tests/total_tests)*100:.1f}%")
    
    print(f"\n{'='*60}")
    print("📋 DETTAGLIO RISULTATI")
    print(f"{'='*60}")
    
    for test_name, success, duration, error in results:
        status = "✅ SUCCESSO" if success else "❌ FALLITO"
        print(f"{status:<12} | {test_name:<30} | {duration:>6.2f}s")
        if not success and error:
            print(f"             Errore: {error[:100]}...")
    
    # Statistiche finali
    if successful_tests == total_tests:
        print(f"\n🎉 TUTTI I TEST COMPLETATI CON SUCCESSO!")
        print("✨ La libreria CAM è pronta per l'uso!")
    else:
        print(f"\n⚠️  {total_tests - successful_tests} test hanno avuto problemi.")
        print("🔧 Controlla gli errori sopra per maggiori dettagli.")
    
    print(f"\n{'='*60}")
    print("🏁 TEST SUITE COMPLETATA")
    print(f"{'='*60}")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
