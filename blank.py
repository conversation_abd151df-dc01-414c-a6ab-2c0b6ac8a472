"""
Modulo per la gestione dei grezzi (blank/stock) per lavorazioni CAM.

Supporta diversi tipi di grezzi:
- Cilindrico
- Rettangolare/Quadrato
- Basato su modello 3D
- Con offset personalizzabili
"""

from abc import ABC, abstractmethod
from typing import Tuple, Optional, Union
import numpy as np

# Import OCC opzionali
try:
    from OCC.Core.gp import gp_Pnt, gp_Vec, gp_Ax2, gp_Dir
    from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox, BRepPrimAPI_MakeCylinder
    from OCC.Core.TopoDS import TopoDS_Shape
    from OCC.Core.BRepAlgoAPI import BRepAlgoAPI_Common
    OCC_AVAILABLE = True
except ImportError:
    OCC_AVAILABLE = False
    # Definisci classi placeholder
    class TopoDS_Shape:
        pass


class Blank(ABC):
    """Classe base astratta per tutti i tipi di grezzi."""

    def __init__(self, name: str, material: str = "Aluminum"):
        """
        Inizializza il grezzo base.

        Args:
            name: Nome identificativo del grezzo
            material: Materiale del grezzo
        """
        self.name = name
        self.material = material
        self.offset_x = 0.0
        self.offset_y = 0.0
        self.offset_z = 0.0
        self._shape = None

    @abstractmethod
    def create_shape(self) -> TopoDS_Shape:
        """Crea la forma geometrica del grezzo."""
        pass

    def set_offset(self, x: float = 0.0, y: float = 0.0, z: float = 0.0):
        """
        Imposta gli offset dal modello da lavorare.

        Args:
            x, y, z: Offset nelle tre direzioni in mm
        """
        self.offset_x = x
        self.offset_y = y
        self.offset_z = z

    def get_shape(self) -> TopoDS_Shape:
        """Restituisce la forma del grezzo."""
        if self._shape is None:
            self._shape = self.create_shape()
        return self._shape

    def get_bounding_box(self) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
        """
        Calcola il bounding box del grezzo.

        Returns:
            Tuple con punto minimo e massimo del bounding box
        """
        if not OCC_AVAILABLE:
            # Calcolo approssimativo senza OCC
            return self._get_approximate_bbox()

        try:
            from OCC.Core.Bnd import Bnd_Box
            from OCC.Core.BRepBndLib import brepbndlib_Add

            bbox = Bnd_Box()
            brepbndlib_Add(self.get_shape(), bbox)
            xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()
            return ((xmin, ymin, zmin), (xmax, ymax, zmax))
        except:
            return self._get_approximate_bbox()

    def get_volume(self) -> float:
        """Calcola il volume del grezzo."""
        if not OCC_AVAILABLE:
            return self._get_approximate_volume()

        try:
            from OCC.Core.GProp import GProp_GProps
            from OCC.Core.BRepGProp import brepgprop_VolumeProperties

            props = GProp_GProps()
            brepgprop_VolumeProperties(self.get_shape(), props)
            return props.Mass()
        except:
            return self._get_approximate_volume()

    def _get_approximate_bbox(self) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
        """Calcolo approssimativo del bounding box senza OCC."""
        return ((0, 0, 0), (10, 10, 10))  # Placeholder

    def _get_approximate_volume(self) -> float:
        """Calcolo approssimativo del volume senza OCC."""
        return 1000.0  # Placeholder


class CylindricalBlank(Blank):
    """Grezzo cilindrico."""

    def __init__(self, name: str, diameter: float, height: float,
                 center: Tuple[float, float, float] = (0, 0, 0),
                 axis_direction: Tuple[float, float, float] = (0, 0, 1),
                 material: str = "Aluminum"):
        """
        Inizializza un grezzo cilindrico.

        Args:
            name: Nome del grezzo
            diameter: Diametro del cilindro in mm
            height: Altezza del cilindro in mm
            center: Centro del cilindro (x, y, z)
            axis_direction: Direzione dell'asse del cilindro
            material: Materiale
        """
        super().__init__(name, material)
        self.diameter = diameter
        self.radius = diameter / 2.0
        self.height = height
        self.center = center
        self.axis_direction = axis_direction

    def create_shape(self) -> TopoDS_Shape:
        """Crea la forma cilindrica."""
        if not OCC_AVAILABLE:
            return TopoDS_Shape()  # Placeholder

        try:
            # Punto di origine
            origin = gp_Pnt(
                self.center[0] + self.offset_x,
                self.center[1] + self.offset_y,
                self.center[2] + self.offset_z
            )

            # Direzione dell'asse
            direction = gp_Dir(
                self.axis_direction[0],
                self.axis_direction[1],
                self.axis_direction[2]
            )

            # Sistema di coordinate
            axis = gp_Ax2(origin, direction)

            # Crea il cilindro
            cylinder_maker = BRepPrimAPI_MakeCylinder(axis, self.radius, self.height)
            return cylinder_maker.Shape()
        except:
            return TopoDS_Shape()  # Placeholder

    def set_diameter(self, diameter: float):
        """Modifica il diametro del cilindro."""
        self.diameter = diameter
        self.radius = diameter / 2.0
        self._shape = None  # Forza la rigenerazione

    def set_height(self, height: float):
        """Modifica l'altezza del cilindro."""
        self.height = height
        self._shape = None  # Forza la rigenerazione


class RectangularBlank(Blank):
    """Grezzo rettangolare/quadrato."""

    def __init__(self, name: str, length: float, width: float, height: float,
                 corner: Tuple[float, float, float] = (0, 0, 0),
                 material: str = "Aluminum"):
        """
        Inizializza un grezzo rettangolare.

        Args:
            name: Nome del grezzo
            length: Lunghezza in mm (direzione X)
            width: Larghezza in mm (direzione Y)
            height: Altezza in mm (direzione Z)
            corner: Punto dell'angolo inferiore (x, y, z)
            material: Materiale
        """
        super().__init__(name, material)
        self.length = length
        self.width = width
        self.height = height
        self.corner = corner

    def create_shape(self) -> TopoDS_Shape:
        """Crea la forma rettangolare."""
        if not OCC_AVAILABLE:
            return TopoDS_Shape()  # Placeholder

        try:
            # Punto dell'angolo con offset
            corner_point = gp_Pnt(
                self.corner[0] + self.offset_x,
                self.corner[1] + self.offset_y,
                self.corner[2] + self.offset_z
            )

            # Crea il parallelepipedo
            box_maker = BRepPrimAPI_MakeBox(corner_point, self.length, self.width, self.height)
            return box_maker.Shape()
        except:
            return TopoDS_Shape()  # Placeholder

    def set_dimensions(self, length: float, width: float, height: float):
        """Modifica le dimensioni del grezzo."""
        self.length = length
        self.width = width
        self.height = height
        self._shape = None  # Forza la rigenerazione

    @classmethod
    def create_square(cls, name: str, side: float, height: float,
                     corner: Tuple[float, float, float] = (0, 0, 0),
                     material: str = "Aluminum"):
        """
        Crea un grezzo quadrato.

        Args:
            name: Nome del grezzo
            side: Lato del quadrato in mm
            height: Altezza in mm
            corner: Punto dell'angolo inferiore
            material: Materiale
        """
        return cls(name, side, side, height, corner, material)


class ModelBasedBlank(Blank):
    """Grezzo basato su un modello 3D esistente."""

    def __init__(self, name: str, base_shape: TopoDS_Shape,
                 uniform_offset: float = 0.0,
                 offset_x: float = 0.0, offset_y: float = 0.0, offset_z: float = 0.0,
                 material: str = "Aluminum"):
        """
        Inizializza un grezzo basato su modello.

        Args:
            name: Nome del grezzo
            base_shape: Forma base da cui derivare il grezzo
            uniform_offset: Offset uniforme in tutte le direzioni
            offset_x, offset_y, offset_z: Offset direzionali
            material: Materiale
        """
        super().__init__(name, material)
        self.base_shape = base_shape
        self.uniform_offset = uniform_offset
        self.set_offset(offset_x, offset_y, offset_z)

    def create_shape(self) -> TopoDS_Shape:
        """Crea la forma basata sul modello con offset."""
        if self.uniform_offset == 0.0 and all(o == 0.0 for o in [self.offset_x, self.offset_y, self.offset_z]):
            return self.base_shape

        # TODO: Implementare offset della forma usando OCC
        # Per ora restituisce la forma base
        return self.base_shape

    def set_base_shape(self, shape: TopoDS_Shape):
        """Modifica la forma base."""
        self.base_shape = shape
        self._shape = None  # Forza la rigenerazione

    def set_uniform_offset(self, offset: float):
        """Imposta un offset uniforme."""
        self.uniform_offset = offset
        self._shape = None  # Forza la rigenerazione


def create_blank_from_model(model_shape: TopoDS_Shape, blank_type: str = "cylindrical",
                           offset: float = 5.0, material: str = "Aluminum") -> Blank:
    """
    Crea automaticamente un grezzo appropriato basato su un modello.

    Args:
        model_shape: Forma del modello da lavorare
        blank_type: Tipo di grezzo ("cylindrical", "rectangular", "model_based")
        offset: Offset dal modello in mm
        material: Materiale del grezzo

    Returns:
        Istanza del grezzo appropriato
    """
    # Calcola bounding box del modello
    from OCC.Core.Bnd import Bnd_Box
    from OCC.Core.BRepBndLib import brepbndlib_Add

    bbox = Bnd_Box()
    brepbndlib_Add(model_shape, bbox)
    xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()

    length = xmax - xmin + 2 * offset
    width = ymax - ymin + 2 * offset
    height = zmax - zmin + 2 * offset

    if blank_type == "cylindrical":
        # Usa il diametro maggiore tra lunghezza e larghezza
        diameter = max(length, width)
        center = ((xmin + xmax) / 2, (ymin + ymax) / 2, zmin - offset)
        return CylindricalBlank("Auto_Cylindrical", diameter, height, center, material=material)

    elif blank_type == "rectangular":
        corner = (xmin - offset, ymin - offset, zmin - offset)
        return RectangularBlank("Auto_Rectangular", length, width, height, corner, material=material)

    elif blank_type == "model_based":
        return ModelBasedBlank("Auto_ModelBased", model_shape, uniform_offset=offset, material=material)

    else:
        raise ValueError(f"Tipo di grezzo non supportato: {blank_type}")
