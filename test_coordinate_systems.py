"""
Test script per i sistemi di coordinate con visualizzazione PyVista.

Questo script testa:
1. Creazione di sistemi di coordinate
2. Trasformazioni di punti
3. Visualizzazione degli assi con PyVista
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pyvista as pv
import numpy as np
from coordinate_system import CoordinateSystem, AxisType, AxisConfig


def create_axis_arrow(origin, direction, length=50, color='red', label='X'):
    """Crea una freccia per rappresentare un asse."""
    end_point = [origin[i] + direction[i] * length for i in range(3)]
    
    # Crea linea
    line = pv.Line(origin, end_point)
    
    # Crea freccia alla fine
    arrow_start = [end_point[i] - direction[i] * length * 0.1 for i in range(3)]
    arrow = pv.Arrow(start=arrow_start, direction=direction, scale=length * 0.1)
    
    return line, arrow


def visualize_coordinate_system(coord_system, plotter, offset=(0, 0, 0)):
    """Visualizza un sistema di coordinate in PyVista."""
    origin = [coord_system.origin[i] + offset[i] for i in range(3)]
    
    # Assi standard
    axes_data = [
        ('X', (1, 0, 0), 'red'),
        ('Y', (0, 1, 0), 'green'), 
        ('Z', (0, 0, 1), 'blue')
    ]
    
    for axis_name, direction, color in axes_data:
        if axis_name in coord_system.axes:
            axis_config = coord_system.axes[axis_name]
            actual_direction = axis_config.get_direction_vector()
            
            line, arrow = create_axis_arrow(origin, actual_direction, length=30, color=color)
            plotter.add_mesh(line, color=color, line_width=3, label=f'{axis_name} Axis')
            plotter.add_mesh(arrow, color=color)
    
    # Aggiungi etichetta origine
    plotter.add_point_labels([origin], [f"{coord_system.name}\n{origin}"], 
                           point_size=8, font_size=12)


def test_basic_coordinate_system():
    """Test sistema di coordinate base."""
    print("=== Test Sistema di Coordinate Base ===")
    
    # Crea sistema di coordinate standard
    coord_system = CoordinateSystem("Standard")
    coord_system.set_origin(0, 0, 0)
    
    print(f"Nome: {coord_system.name}")
    print(f"Origine: {coord_system.origin}")
    print(f"Assi configurati: {list(coord_system.axes.keys())}")
    
    # Test trasformazione punti
    test_points = [
        (10, 20, 30),
        (-5, 15, 25),
        (0, 0, 0)
    ]
    
    print("\nTrasformazioni punti:")
    for point in test_points:
        transformed = coord_system.apply_transformation(point)
        print(f"  {point} -> {transformed}")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Sistema di Coordinate Standard")
    
    # Aggiungi sistema di coordinate
    visualize_coordinate_system(coord_system, plotter)
    
    # Aggiungi punti di test
    points_array = np.array(test_points)
    plotter.add_points(points_array, color='purple', point_size=10, label='Punti Test')
    
    # Aggiungi punti trasformati
    transformed_points = [coord_system.apply_transformation(p) for p in test_points]
    transformed_array = np.array(transformed_points)
    plotter.add_points(transformed_array, color='orange', point_size=8, label='Punti Trasformati')
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return coord_system


def test_offset_coordinate_system():
    """Test sistema di coordinate con offset."""
    print("\n=== Test Sistema di Coordinate con Offset ===")
    
    # Crea sistema con origine spostata
    coord_system = CoordinateSystem("Offset")
    coord_system.set_origin(50, 30, 20)
    
    print(f"Nome: {coord_system.name}")
    print(f"Origine: {coord_system.origin}")
    
    # Test trasformazione
    test_points = [
        (60, 40, 30),  # Punto vicino all'origine
        (0, 0, 0),     # Origine globale
        (100, 60, 40)  # Punto lontano
    ]
    
    print("\nTrasformazioni con offset:")
    for point in test_points:
        transformed = coord_system.apply_transformation(point)
        print(f"  {point} -> {transformed}")
    
    # Visualizzazione comparativa
    plotter = pv.Plotter(title="Confronto Sistemi di Coordinate")
    
    # Sistema standard (origine 0,0,0)
    standard_system = CoordinateSystem("Standard")
    standard_system.set_origin(0, 0, 0)
    visualize_coordinate_system(standard_system, plotter)
    
    # Sistema con offset
    visualize_coordinate_system(coord_system, plotter)
    
    # Punti di test
    points_array = np.array(test_points)
    plotter.add_points(points_array, color='red', point_size=10, label='Punti Originali')
    
    # Punti trasformati dal sistema offset
    transformed_points = [coord_system.apply_transformation(p) for p in test_points]
    transformed_array = np.array(transformed_points)
    plotter.add_points(transformed_array, color='blue', point_size=8, label='Punti Trasformati')
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return coord_system


def test_inverted_axes():
    """Test sistema con assi invertiti."""
    print("\n=== Test Sistema con Assi Invertiti ===")
    
    # Crea sistema con asse Y invertito
    coord_system = CoordinateSystem("Y_Invertito")
    coord_system.set_origin(0, 0, 0)
    
    # Configura asse Y invertito
    coord_system.configure_axis('Y', AxisType.LINEAR, inverted=True)
    
    print(f"Nome: {coord_system.name}")
    print(f"Configurazione assi:")
    for name, axis in coord_system.axes.items():
        direction = axis.get_direction_vector()
        print(f"  {name}: {direction} (invertito: {axis.inverted})")
    
    # Test trasformazione
    test_points = [
        (10, 10, 10),
        (0, 20, 0),
        (-5, -5, 15)
    ]
    
    print("\nTrasformazioni con asse Y invertito:")
    for point in test_points:
        transformed = coord_system.apply_transformation(point)
        print(f"  {point} -> {transformed}")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Sistema con Asse Y Invertito")
    
    # Sistema standard per confronto
    standard_system = CoordinateSystem("Standard")
    standard_system.set_origin(0, 0, 0)
    visualize_coordinate_system(standard_system, plotter, offset=(-40, 0, 0))
    
    # Sistema con Y invertito
    visualize_coordinate_system(coord_system, plotter, offset=(40, 0, 0))
    
    # Punti di test
    points_array = np.array(test_points)
    plotter.add_points(points_array, color='green', point_size=10, label='Punti Test')
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return coord_system


def test_work_offsets():
    """Test work offsets (G54, G55, etc.)."""
    print("\n=== Test Work Offsets ===")
    
    # Crea sistema base
    base_system = CoordinateSystem("Macchina")
    base_system.set_origin(0, 0, 0)
    
    # Crea diversi work offset
    offsets = [
        (1, "Pezzo_1", (100, 50, 25)),
        (2, "Pezzo_2", (200, 100, 25)),
        (3, "Pezzo_3", (150, 150, 50))
    ]
    
    work_offset_systems = []
    for offset_num, name, origin in offsets:
        work_offset = base_system.create_work_offset(offset_num, name)
        work_offset.coordinate_system.set_origin(*origin)
        work_offset_systems.append((work_offset, origin))
        
        print(f"Work Offset G{53 + offset_num}: {name} at {origin}")
        print(f"  G-code: {work_offset.get_gcode_command()}")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Work Offsets (G54, G55, G56)")
    
    # Sistema macchina
    visualize_coordinate_system(base_system, plotter)
    
    # Work offsets
    colors = ['red', 'green', 'blue']
    for i, (work_offset, origin) in enumerate(work_offset_systems):
        visualize_coordinate_system(work_offset.coordinate_system, plotter)
        
        # Aggiungi etichetta work offset
        plotter.add_point_labels([origin], [f"G{54 + i}"], 
                               point_size=12, font_size=14, text_color=colors[i])
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return work_offset_systems


def main():
    """Esegue tutti i test."""
    print("Test Sistemi di Coordinate con Visualizzazione PyVista")
    print("=" * 60)
    
    try:
        # Test 1: Sistema base
        standard_system = test_basic_coordinate_system()
        
        # Test 2: Sistema con offset
        offset_system = test_offset_coordinate_system()
        
        # Test 3: Assi invertiti
        inverted_system = test_inverted_axes()
        
        # Test 4: Work offsets
        work_offsets = test_work_offsets()
        
        print("\n" + "=" * 60)
        print("Tutti i test completati con successo!")
        
        # Riepilogo sistemi
        print("\nSistemi di coordinate testati:")
        systems = [standard_system, offset_system, inverted_system]
        for system in systems:
            print(f"  {system.name}: origine {system.origin}")
            
    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
