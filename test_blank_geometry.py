"""
Test script per la geometria dei grezzi con visualizzazione PyVista.

Questo script testa:
1. Creazione di grezzi cilindrici e rettangolari
2. Calcolo di volumi e bounding box
3. Visualizzazione 3D con PyVista
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pyvista as pv
import numpy as np
from blank import CylindricalBlank, RectangularBlank, create_blank_from_bbox, GeometricShape


def create_pyvista_cylinder(center, radius, height):
    """Crea un cilindro PyVista."""
    cylinder = pv.Cylinder(
        center=center,
        direction=(0, 0, 1),
        radius=radius,
        height=height
    )
    return cylinder


def create_pyvista_box(corner, length, width, height):
    """Crea un parallelepipedo PyVista."""
    box = pv.Box(
        bounds=[
            corner[0], corner[0] + length,
            corner[1], corner[1] + width,
            corner[2], corner[2] + height
        ]
    )
    return box


def test_cylindrical_blank():
    """Test per grezzo cilindrico."""
    print("=== Test Grezzo Cilindrico ===")
    
    # Crea grezzo cilindrico
    blank = CylindricalBlank(
        name="Cilindro_Test",
        diameter=50.0,
        height=100.0,
        center=(0, 0, 0),
        material="Aluminum"
    )
    
    # Test proprietà
    print(f"Nome: {blank.name}")
    print(f"Materiale: {blank.material}")
    print(f"Diametro: {blank.diameter} mm")
    print(f"Altezza: {blank.height} mm")
    print(f"Centro: {blank.center}")
    
    # Test calcoli geometrici
    shape = blank.get_shape()
    volume = blank.get_volume()
    bbox = blank.get_bounding_box()
    
    print(f"Volume: {volume:.2f} mm³")
    print(f"Bounding Box: {bbox}")
    
    # Test contenimento punti
    test_points = [
        (0, 0, 50),      # Centro
        (20, 0, 50),     # Dentro
        (30, 0, 50),     # Fuori (raggio > 25)
        (0, 0, 150),     # Fuori (altezza > 100)
    ]
    
    print("\nTest contenimento punti:")
    for point in test_points:
        inside = blank.contains_point(point)
        print(f"  Punto {point}: {'Dentro' if inside else 'Fuori'}")
    
    # Visualizzazione PyVista
    plotter = pv.Plotter(title="Test Grezzo Cilindrico")
    
    # Aggiungi cilindro
    cylinder_mesh = create_pyvista_cylinder(
        center=(blank.center[0], blank.center[1], blank.center[2] + blank.height/2),
        radius=blank.radius,
        height=blank.height
    )
    plotter.add_mesh(cylinder_mesh, color='lightblue', opacity=0.7, label='Grezzo Cilindrico')
    
    # Aggiungi punti di test
    points_array = np.array(test_points)
    colors = ['red' if not blank.contains_point(p) else 'green' for p in test_points]
    plotter.add_points(points_array, color=colors, point_size=10, label='Punti Test')
    
    # Aggiungi bounding box
    bbox_mesh = create_pyvista_box(bbox[0], 
                                   bbox[1][0] - bbox[0][0],
                                   bbox[1][1] - bbox[0][1], 
                                   bbox[1][2] - bbox[0][2])
    plotter.add_mesh(bbox_mesh, style='wireframe', color='black', label='Bounding Box')
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return blank


def test_rectangular_blank():
    """Test per grezzo rettangolare."""
    print("\n=== Test Grezzo Rettangolare ===")
    
    # Crea grezzo rettangolare
    blank = RectangularBlank(
        name="Parallelepipedo_Test",
        length=80.0,
        width=60.0,
        height=40.0,
        corner=(0, 0, 0),
        material="Steel"
    )
    
    # Test proprietà
    print(f"Nome: {blank.name}")
    print(f"Materiale: {blank.material}")
    print(f"Dimensioni: {blank.length} x {blank.width} x {blank.height} mm")
    print(f"Angolo: {blank.corner}")
    
    # Test calcoli geometrici
    shape = blank.get_shape()
    volume = blank.get_volume()
    bbox = blank.get_bounding_box()
    
    print(f"Volume: {volume:.2f} mm³")
    print(f"Bounding Box: {bbox}")
    
    # Test contenimento punti
    test_points = [
        (40, 30, 20),    # Centro
        (10, 10, 10),    # Dentro
        (90, 30, 20),    # Fuori (x > 80)
        (40, 70, 20),    # Fuori (y > 60)
        (40, 30, 50),    # Fuori (z > 40)
    ]
    
    print("\nTest contenimento punti:")
    for point in test_points:
        inside = blank.contains_point(point)
        print(f"  Punto {point}: {'Dentro' if inside else 'Fuori'}")
    
    # Visualizzazione PyVista
    plotter = pv.Plotter(title="Test Grezzo Rettangolare")
    
    # Aggiungi parallelepipedo
    box_mesh = create_pyvista_box(blank.corner, blank.length, blank.width, blank.height)
    plotter.add_mesh(box_mesh, color='lightgreen', opacity=0.7, label='Grezzo Rettangolare')
    
    # Aggiungi punti di test
    points_array = np.array(test_points)
    colors = ['red' if not blank.contains_point(p) else 'green' for p in test_points]
    plotter.add_points(points_array, color=colors, point_size=10, label='Punti Test')
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return blank


def test_blank_creation_from_bbox():
    """Test creazione automatica grezzi da bounding box."""
    print("\n=== Test Creazione Automatica Grezzi ===")
    
    # Definisce bounding box di un modello
    model_bbox = ((-20, -15, 0), (20, 15, 30))
    
    # Crea diversi tipi di grezzo
    cylindrical = create_blank_from_bbox(model_bbox, "cylindrical", offset=5.0, material="Aluminum")
    rectangular = create_blank_from_bbox(model_bbox, "rectangular", offset=3.0, material="Steel")
    
    print(f"Grezzo cilindrico: Ø{cylindrical.diameter} x {cylindrical.height} mm")
    print(f"Grezzo rettangolare: {rectangular.length} x {rectangular.width} x {rectangular.height} mm")
    
    # Visualizzazione comparativa
    plotter = pv.Plotter(title="Confronto Grezzi Automatici")
    
    # Modello originale (bounding box)
    model_mesh = create_pyvista_box(model_bbox[0], 
                                    model_bbox[1][0] - model_bbox[0][0],
                                    model_bbox[1][1] - model_bbox[0][1],
                                    model_bbox[1][2] - model_bbox[0][2])
    plotter.add_mesh(model_mesh, color='red', opacity=0.5, label='Modello Originale')
    
    # Grezzo cilindrico
    cyl_mesh = create_pyvista_cylinder(
        center=(cylindrical.center[0], cylindrical.center[1], cylindrical.center[2] + cylindrical.height/2),
        radius=cylindrical.radius,
        height=cylindrical.height
    )
    plotter.add_mesh(cyl_mesh, color='lightblue', opacity=0.3, label='Grezzo Cilindrico')
    
    # Grezzo rettangolare
    rect_mesh = create_pyvista_box(rectangular.corner, rectangular.length, rectangular.width, rectangular.height)
    plotter.add_mesh(rect_mesh, color='lightgreen', opacity=0.3, label='Grezzo Rettangolare')
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return cylindrical, rectangular


def main():
    """Esegue tutti i test."""
    print("Test Geometria Grezzi con Visualizzazione PyVista")
    print("=" * 60)
    
    try:
        # Test 1: Grezzo cilindrico
        cyl_blank = test_cylindrical_blank()
        
        # Test 2: Grezzo rettangolare
        rect_blank = test_rectangular_blank()
        
        # Test 3: Creazione automatica
        auto_cyl, auto_rect = test_blank_creation_from_bbox()
        
        print("\n" + "=" * 60)
        print("Tutti i test completati con successo!")
        
        # Riepilogo volumi
        print("\nRiepilogo volumi:")
        blanks = [cyl_blank, rect_blank, auto_cyl, auto_rect]
        for blank in blanks:
            print(f"  {blank.name}: {blank.get_volume():.2f} mm³")
            
    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
