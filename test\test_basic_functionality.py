"""
Test di base per verificare che la libreria CAM funzioni senza PyVista.

Questo test verifica le funzionalità core senza dipendenze di visualizzazione.
"""

import sys
import os
# Aggiungi il percorso della libreria principale
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_blank_creation():
    """Test creazione grezzi."""
    print("=== Test Creazione Grezzi ===")
    
    try:
        from blank import CylindricalBlank, RectangularBlank, create_blank_from_bbox
        
        # Test grezzo cilindrico
        cyl_blank = CylindricalBlank(
            name="Test_Cilindro",
            diameter=50.0,
            height=100.0,
            center=(0, 0, 0),
            material="Aluminum"
        )
        
        print(f"✅ Grezzo cilindrico creato: {cyl_blank.name}")
        print(f"   Volume: {cyl_blank.get_volume():.2f} mm³")
        print(f"   BBox: {cyl_blank.get_bounding_box()}")
        
        # Test grezzo rettangolare
        rect_blank = RectangularBlank(
            name="Test_Rettangolo",
            length=80.0,
            width=60.0,
            height=40.0,
            corner=(0, 0, 0),
            material="Steel"
        )
        
        print(f"✅ Grezzo rettangolare creato: {rect_blank.name}")
        print(f"   Volume: {rect_blank.get_volume():.2f} mm³")
        print(f"   BBox: {rect_blank.get_bounding_box()}")
        
        # Test creazione automatica
        bbox = ((-10, -10, 0), (10, 10, 20))
        auto_blank = create_blank_from_bbox(bbox, "cylindrical", offset=2.0)
        
        print(f"✅ Grezzo automatico creato: {auto_blank.name}")
        print(f"   Diametro: {auto_blank.diameter} mm")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test grezzi: {e}")
        return False


def test_coordinate_systems():
    """Test sistemi di coordinate."""
    print("\n=== Test Sistemi di Coordinate ===")
    
    try:
        from coordinate_system import CoordinateSystem, AxisType
        
        # Test sistema base
        coord_system = CoordinateSystem("Test_System")
        coord_system.set_origin(10, 20, 30)
        
        print(f"✅ Sistema di coordinate creato: {coord_system.name}")
        print(f"   Origine: {coord_system.origin}")
        
        # Test trasformazione
        test_point = (50, 60, 70)
        transformed = coord_system.apply_transformation(test_point)
        
        print(f"✅ Trasformazione punto: {test_point} -> {transformed}")
        
        # Test configurazione assi
        coord_system.configure_axis('Y', AxisType.LINEAR, inverted=True)
        
        print(f"✅ Asse Y configurato come invertito")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test coordinate: {e}")
        return False


def test_operations():
    """Test operazioni CAM."""
    print("\n=== Test Operazioni CAM ===")
    
    try:
        from operations import (
            OperationType, RoughingOperation, ContouringOperation, 
            EngravingOperation, MathematicalSurface
        )
        
        # Classe Tool semplice
        class Tool:
            def __init__(self, name, diameter, length):
                self.name = name
                self.diameter = diameter
                self.length = length
        
        # Test superficie matematica
        surface = MathematicalSurface('plane', {
            'center': (0, 0, 0),
            'size': (50, 30),
            'normal': (0, 0, 1)
        })
        
        print(f"✅ Superficie matematica creata: {surface.surface_type}")
        print(f"   Area: {surface.get_area():.2f} mm²")
        print(f"   BBox: {surface.get_bounding_box()}")
        
        # Test operazione di sgrossatura
        tool = Tool("Fresa_8mm", 8.0, 50.0)
        roughing_op = RoughingOperation("Test_Sgrossatura")
        roughing_op.set_tool(tool)
        roughing_op.add_surface(surface)
        
        roughing_op.set_roughing_parameters(
            stock_to_leave=0.5,
            max_depth=2.0,
            strategy="zigzag"
        )
        
        # Test validazione
        is_valid, errors = roughing_op.validate()
        print(f"✅ Operazione sgrossatura creata e validata: {is_valid}")
        
        if not is_valid:
            print(f"   Errori: {errors}")
        
        # Test calcolo tempo
        machining_time = roughing_op.calculate_machining_time()
        print(f"   Tempo stimato: {machining_time:.2f} min")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test operazioni: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_path_generation():
    """Test generazione percorsi."""
    print("\n=== Test Generazione Percorsi ===")
    
    try:
        from path_generator import PathGenerator, PathPoint, ToolPath
        from operations import RoughingOperation, MathematicalSurface
        
        # Classe Tool semplice
        class Tool:
            def __init__(self, name, diameter, length):
                self.name = name
                self.diameter = diameter
                self.length = length
        
        # Crea operazione semplice
        surface = MathematicalSurface('plane', {
            'center': (0, 0, 0),
            'size': (20, 15),
            'normal': (0, 0, 1)
        })
        
        tool = Tool("Fresa_Test", 6.0, 40.0)
        operation = RoughingOperation("Test_Path")
        operation.set_tool(tool)
        operation.add_surface(surface)
        operation.set_speeds_and_feeds(2000, 200, 50)
        
        # Test generazione percorso
        generator = PathGenerator()
        tool_path = generator.generate_path(operation)
        
        print(f"✅ Percorso generato: {len(tool_path.points)} punti")
        print(f"   Lunghezza: {tool_path.total_length:.2f} mm")
        print(f"   Tempo: {tool_path.machining_time:.2f} min")
        
        # Test punti del percorso
        if tool_path.points:
            first_point = tool_path.points[0]
            print(f"   Primo punto: ({first_point.x:.2f}, {first_point.y:.2f}, {first_point.z:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test percorsi: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_optimization():
    """Test ottimizzazione."""
    print("\n=== Test Ottimizzazione ===")
    
    try:
        from optimization import PathOptimizer, OptimizationSettings
        from path_generator import PathPoint, ToolPath
        from operations import RoughingOperation
        
        # Classe Tool semplice
        class Tool:
            def __init__(self, name, diameter, length):
                self.name = name
                self.diameter = diameter
                self.length = length
        
        # Crea percorso di test
        tool = Tool("Fresa_Test", 4.0, 30.0)
        operation = RoughingOperation("Test_Opt")
        operation.set_tool(tool)
        operation.set_speeds_and_feeds(2000, 150, 40)
        
        # Crea punti di test
        points = []
        for i in range(20):
            x = i * 2.0
            y = 0.0
            z = -1.0
            point = PathPoint(x, y, z, feed_rate=150)
            points.append(point)
        
        original_path = ToolPath(operation, tool)
        original_path.points = points
        original_path._calculate_properties()
        
        print(f"✅ Percorso originale: {len(original_path.points)} punti")
        
        # Test ottimizzazione
        optimizer = PathOptimizer()
        settings = OptimizationSettings()
        settings.enable_point_reduction = True
        settings.point_reduction_tolerance = 0.1
        
        optimized_path = optimizer.optimize_path(original_path, settings)
        
        print(f"✅ Percorso ottimizzato: {len(optimized_path.points)} punti")
        
        reduction = ((len(original_path.points) - len(optimized_path.points)) / len(original_path.points) * 100)
        print(f"   Riduzione punti: {reduction:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test ottimizzazione: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_setup():
    """Test setup completo."""
    print("\n=== Test Setup Completo ===")
    
    try:
        from setup import CAMSetup
        from blank import RectangularBlank
        from coordinate_system import CoordinateSystem
        from operations import OperationType, MathematicalSurface
        
        # Classe Tool semplice
        class Tool:
            def __init__(self, name, diameter, length, code=None):
                self.name = name
                self.diameter = diameter
                self.length = length
                self.code = code
        
        # Crea setup
        setup = CAMSetup("Test_Setup")
        
        # Aggiungi grezzo
        blank = RectangularBlank(
            name="Test_Blank",
            length=50.0,
            width=30.0,
            height=20.0,
            corner=(0, 0, 0),
            material="Aluminum"
        )
        setup.set_blank(blank)
        
        # Aggiungi sistema di coordinate
        coord_system = CoordinateSystem("Test_Coord")
        coord_system.set_origin(25, 15, 20)
        setup.set_coordinate_system(coord_system, work_offset_number=1)
        
        # Aggiungi superficie
        surface = MathematicalSurface('plane', {
            'center': (25, 15, 15),
            'size': (40, 25),
            'normal': (0, 0, 1)
        })
        setup.add_model_surface(surface)
        
        # Aggiungi operazione
        tool = Tool("Fresa_Test", 6.0, 40.0, code="T1")
        operation = setup.create_operation(
            OperationType.ROUGHING,
            "Test_Operation",
            tool,
            [surface]
        )
        
        operation.set_speeds_and_feeds(2000, 200, 50)
        
        print(f"✅ Setup completo creato: {setup.name}")
        print(f"   Grezzo: {setup.blank.name}")
        print(f"   Operazioni: {len(setup.operations)}")
        
        # Test validazione
        is_valid, errors = setup.validate_setup()
        print(f"✅ Setup validato: {is_valid}")
        
        if not is_valid:
            print(f"   Errori: {errors}")
        
        # Test info setup
        setup_info = setup.get_setup_info()
        print(f"   Tempo totale stimato: {setup_info['total_machining_time']:.2f} min")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test setup: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Esegue tutti i test di base."""
    print("🔧 TEST FUNZIONALITÀ BASE LIBRERIA CAM")
    print("=" * 60)
    print("Questi test verificano che la libreria funzioni senza PyVista")
    print("=" * 60)
    
    tests = [
        ("Creazione Grezzi", test_blank_creation),
        ("Sistemi di Coordinate", test_coordinate_systems),
        ("Operazioni CAM", test_operations),
        ("Generazione Percorsi", test_path_generation),
        ("Ottimizzazione", test_optimization),
        ("Setup Completo", test_complete_setup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Errore critico in {test_name}: {e}")
            results.append((test_name, False))
    
    # Riepilogo
    print(f"\n{'='*60}")
    print("📊 RIEPILOGO TEST BASE")
    print(f"{'='*60}")
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ SUCCESSO" if success else "❌ FALLITO"
        print(f"{status} | {test_name}")
    
    print(f"\n📈 Risultato: {successful}/{total} test riusciti")
    print(f"📊 Percentuale successo: {(successful/total)*100:.1f}%")
    
    if successful == total:
        print("\n🎉 TUTTI I TEST BASE COMPLETATI CON SUCCESSO!")
        print("✨ La libreria CAM funziona correttamente!")
    else:
        print(f"\n⚠️  {total - successful} test hanno avuto problemi.")
    
    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
