"""
Test script per la generazione di percorsi CAM con visualizzazione PyVista.

Questo script testa:
1. Generazione percorsi per diverse operazioni
2. Visualizzazione percorsi 3D
3. Analisi lunghezze e tempi
4. Pattern di lavorazione
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pyvista as pv
import numpy as np
from path_generator import PathGenerator, PathPoint, ToolPath
from operations import (
    OperationType, RoughingOperation, ContouringOperation, EngravingOperation,
    MathematicalSurface
)


# Classe placeholder per Tool se non disponibile
class Tool:
    def __init__(self, name, diameter, length, code=None):
        self.name = name
        self.diameter = diameter
        self.length = length
        self.code = code


def create_surface_mesh(surface):
    """Crea una mesh PyVista da una superficie matematica."""
    if surface.surface_type == 'plane':
        center = surface.parameters.get('center', (0, 0, 0))
        size = surface.parameters.get('size', (10, 10))
        
        plane = pv.Plane(
            center=center,
            direction=(0, 0, 1),
            i_size=size[0],
            j_size=size[1]
        )
        return plane
        
    elif surface.surface_type == 'cylinder':
        center = surface.parameters.get('center', (0, 0, 0))
        radius = surface.parameters.get('radius', 1)
        height = surface.parameters.get('height', 1)
        
        cylinder = pv.Cylinder(
            center=(center[0], center[1], center[2] + height/2),
            direction=(0, 0, 1),
            radius=radius,
            height=height
        )
        return cylinder
        
    else:
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox
        
        box = pv.Box(bounds=[
            min_point[0], max_point[0],
            min_point[1], max_point[1], 
            min_point[2], max_point[2]
        ])
        return box


def visualize_tool_path(plotter, tool_path, color='red', line_width=2, label='Tool Path'):
    """Visualizza un percorso utensile in PyVista."""
    if not tool_path.points:
        return
    
    # Estrai coordinate dei punti
    points = []
    for point in tool_path.points:
        points.append([point.x, point.y, point.z])
    
    if len(points) < 2:
        return
    
    # Crea linea del percorso
    points_array = np.array(points)
    
    # Crea spline per percorso fluido
    if len(points) > 2:
        spline = pv.Spline(points_array)
        plotter.add_mesh(spline, color=color, line_width=line_width, label=label)
    else:
        line = pv.Line(points[0], points[1])
        plotter.add_mesh(line, color=color, line_width=line_width, label=label)
    
    # Aggiungi punti di inizio e fine
    start_point = points_array[0:1]
    end_point = points_array[-1:]
    
    plotter.add_points(start_point, color='green', point_size=8, label='Start')
    plotter.add_points(end_point, color='red', point_size=8, label='End')


def test_roughing_path_generation():
    """Test generazione percorso di sgrossatura."""
    print("=== Test Generazione Percorso Sgrossatura ===")
    
    # Crea superficie
    surface = MathematicalSurface('plane', {
        'center': (25, 15, 0),
        'size': (40, 25),
        'normal': (0, 0, 1)
    })
    
    # Crea operazione
    tool = Tool("Fresa_Sgrossatura_8mm", diameter=8.0, length=50.0)
    operation = RoughingOperation("Sgrossatura_Test")
    operation.set_tool(tool)
    operation.add_surface(surface)
    
    # Configura parametri
    operation.set_roughing_parameters(
        stock_to_leave=0.5,
        max_depth=2.0,
        strategy="zigzag"
    )
    operation.set_speeds_and_feeds(2000, 300, 50)
    operation.set_precision(tolerance=0.1, step_down=2.0, step_over=6.0)
    
    # Genera percorso
    generator = PathGenerator()
    
    try:
        tool_path = generator.generate_path(operation)
        
        print(f"Percorso generato: {len(tool_path.points)} punti")
        print(f"Lunghezza totale: {tool_path.total_length:.2f} mm")
        print(f"Tempo lavorazione: {tool_path.machining_time:.2f} min")
        
        # Visualizzazione
        plotter = pv.Plotter(title="Percorso di Sgrossatura")
        
        # Superficie
        surface_mesh = create_surface_mesh(surface)
        plotter.add_mesh(surface_mesh, color='lightblue', opacity=0.5, label='Superficie')
        
        # Percorso
        visualize_tool_path(plotter, tool_path, color='red', label='Percorso Sgrossatura')
        
        # Informazioni
        info_text = f"""Operazione: {operation.name}
Strategia: {operation.roughing_strategy}
Punti percorso: {len(tool_path.points)}
Lunghezza: {tool_path.total_length:.2f} mm
Tempo: {tool_path.machining_time:.2f} min
Step over: {operation.step_over} mm
Step down: {operation.step_down} mm"""
        
        plotter.add_text(info_text, position='upper_left', font_size=10)
        
        plotter.add_legend()
        plotter.show_axes()
        plotter.show()
        
        return tool_path
        
    except Exception as e:
        print(f"Errore nella generazione del percorso: {e}")
        return None


def test_contouring_path_generation():
    """Test generazione percorso di contornatura."""
    print("\n=== Test Generazione Percorso Contornatura ===")
    
    # Crea superficie rettangolare
    surface = MathematicalSurface('plane', {
        'center': (0, 0, 5),
        'size': (30, 20),
        'normal': (0, 0, 1)
    })
    
    # Crea operazione
    tool = Tool("Fresa_Finitura_4mm", diameter=4.0, length=40.0)
    operation = ContouringOperation("Contornatura_Test")
    operation.set_tool(tool)
    operation.add_surface(surface)
    
    # Configura parametri
    operation.set_contouring_parameters(
        finish_passes=2,
        spring_passes=1,
        lead_in=3.0,
        lead_out=3.0
    )
    operation.set_speeds_and_feeds(3000, 200, 30)
    operation.set_precision(tolerance=0.02)
    
    # Genera percorso
    generator = PathGenerator()
    
    try:
        tool_path = generator.generate_path(operation)
        
        print(f"Percorso generato: {len(tool_path.points)} punti")
        print(f"Lunghezza totale: {tool_path.total_length:.2f} mm")
        print(f"Tempo lavorazione: {tool_path.machining_time:.2f} min")
        
        # Visualizzazione
        plotter = pv.Plotter(title="Percorso di Contornatura")
        
        # Superficie
        surface_mesh = create_surface_mesh(surface)
        plotter.add_mesh(surface_mesh, color='lightgreen', opacity=0.5, label='Superficie')
        
        # Percorso
        visualize_tool_path(plotter, tool_path, color='blue', label='Percorso Contornatura')
        
        # Informazioni
        info_text = f"""Operazione: {operation.name}
Passate finitura: {operation.finish_passes}
Passate a vuoto: {operation.spring_passes}
Punti percorso: {len(tool_path.points)}
Lunghezza: {tool_path.total_length:.2f} mm
Tempo: {tool_path.machining_time:.2f} min
Lead-in/out: {operation.lead_in_distance} mm"""
        
        plotter.add_text(info_text, position='upper_left', font_size=10)
        
        plotter.add_legend()
        plotter.show_axes()
        plotter.show()
        
        return tool_path
        
    except Exception as e:
        print(f"Errore nella generazione del percorso: {e}")
        return None


def test_engraving_path_generation():
    """Test generazione percorso di incisione."""
    print("\n=== Test Generazione Percorso Incisione ===")
    
    # Crea superficie cilindrica
    surface = MathematicalSurface('cylinder', {
        'center': (0, 0, 0),
        'radius': 15,
        'height': 40
    })
    
    # Crea operazione
    tool = Tool("Fresa_Incisione_1mm", diameter=1.0, length=30.0)
    operation = EngravingOperation("Incisione_Test")
    operation.set_tool(tool)
    operation.add_surface(surface)
    
    # Configura parametri
    operation.set_engraving_parameters(
        depth=0.2,
        text="TEST",
        font_size=6.0
    )
    operation.set_speeds_and_feeds(5000, 100, 25)
    
    # Genera percorso
    generator = PathGenerator()
    
    try:
        tool_path = generator.generate_path(operation)
        
        print(f"Percorso generato: {len(tool_path.points)} punti")
        print(f"Lunghezza totale: {tool_path.total_length:.2f} mm")
        print(f"Tempo lavorazione: {tool_path.machining_time:.2f} min")
        
        # Visualizzazione
        plotter = pv.Plotter(title="Percorso di Incisione")
        
        # Superficie cilindrica
        surface_mesh = create_surface_mesh(surface)
        plotter.add_mesh(surface_mesh, color='lightyellow', opacity=0.5, label='Superficie')
        
        # Percorso
        visualize_tool_path(plotter, tool_path, color='purple', label='Percorso Incisione')
        
        # Informazioni
        info_text = f"""Operazione: {operation.name}
Testo: {operation.text_content}
Profondità: {operation.engraving_depth} mm
Punti percorso: {len(tool_path.points)}
Lunghezza: {tool_path.total_length:.2f} mm
Tempo: {tool_path.machining_time:.2f} min"""
        
        plotter.add_text(info_text, position='upper_left', font_size=10)
        
        plotter.add_legend()
        plotter.show_axes()
        plotter.show()
        
        return tool_path
        
    except Exception as e:
        print(f"Errore nella generazione del percorso: {e}")
        return None


def test_multiple_paths_comparison():
    """Test confronto di percorsi multipli."""
    print("\n=== Test Confronto Percorsi Multipli ===")
    
    # Crea superficie comune
    surface = MathematicalSurface('plane', {
        'center': (0, 0, 0),
        'size': (50, 30),
        'normal': (0, 0, 1)
    })
    
    # Crea diverse operazioni
    operations = []
    
    # Sgrossatura
    roughing_tool = Tool("Fresa_8mm", diameter=8.0, length=50.0)
    roughing_op = RoughingOperation("Sgrossatura")
    roughing_op.set_tool(roughing_tool)
    roughing_op.add_surface(surface)
    roughing_op.set_roughing_parameters(stock_to_leave=0.5, max_depth=2.0, strategy="zigzag")
    roughing_op.set_speeds_and_feeds(2000, 300, 50)
    operations.append(roughing_op)
    
    # Finitura
    finishing_tool = Tool("Fresa_4mm", diameter=4.0, length=40.0)
    finishing_op = ContouringOperation("Finitura")
    finishing_op.set_tool(finishing_tool)
    finishing_op.add_surface(surface)
    finishing_op.set_contouring_parameters(finish_passes=1, spring_passes=0)
    finishing_op.set_speeds_and_feeds(3000, 200, 30)
    operations.append(finishing_op)
    
    # Genera percorsi
    generator = PathGenerator()
    tool_paths = []
    
    for operation in operations:
        try:
            tool_path = generator.generate_path(operation)
            tool_paths.append(tool_path)
            print(f"{operation.name}: {len(tool_path.points)} punti, {tool_path.total_length:.2f} mm")
        except Exception as e:
            print(f"Errore per {operation.name}: {e}")
    
    # Visualizzazione comparativa
    plotter = pv.Plotter(title="Confronto Percorsi Multipli")
    
    # Superficie
    surface_mesh = create_surface_mesh(surface)
    plotter.add_mesh(surface_mesh, color='lightgray', opacity=0.3, label='Superficie')
    
    # Percorsi con colori diversi
    colors = ['red', 'blue', 'green', 'orange']
    for i, tool_path in enumerate(tool_paths):
        if tool_path:
            color = colors[i % len(colors)]
            label = f"{tool_path.operation.name}"
            visualize_tool_path(plotter, tool_path, color=color, label=label)
    
    # Statistiche
    total_time = sum(tp.machining_time for tp in tool_paths if tp)
    total_length = sum(tp.total_length for tp in tool_paths if tp)
    
    stats_text = f"""Statistiche Totali:
Operazioni: {len(tool_paths)}
Lunghezza totale: {total_length:.2f} mm
Tempo totale: {total_time:.2f} min
Punti totali: {sum(len(tp.points) for tp in tool_paths if tp)}"""
    
    plotter.add_text(stats_text, position='upper_right', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return tool_paths


def main():
    """Esegue tutti i test."""
    print("Test Generazione Percorsi CAM con Visualizzazione PyVista")
    print("=" * 60)
    
    try:
        # Test 1: Percorso sgrossatura
        roughing_path = test_roughing_path_generation()
        
        # Test 2: Percorso contornatura
        contouring_path = test_contouring_path_generation()
        
        # Test 3: Percorso incisione
        engraving_path = test_engraving_path_generation()
        
        # Test 4: Confronto percorsi multipli
        multiple_paths = test_multiple_paths_comparison()
        
        print("\n" + "=" * 60)
        print("Tutti i test completati con successo!")
        
        # Riepilogo percorsi
        print("\nPercorsi generati:")
        all_paths = [roughing_path, contouring_path, engraving_path] + (multiple_paths or [])
        valid_paths = [p for p in all_paths if p is not None]
        
        for path in valid_paths:
            if hasattr(path, 'operation'):
                print(f"  {path.operation.name}: {len(path.points)} punti, {path.total_length:.2f} mm")
            
    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
