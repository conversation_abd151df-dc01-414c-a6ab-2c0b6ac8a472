from typing import Dict, Tuple, List, Optional, Any
from .coordinate_system import CoordinateSystem, WorkOffset
from .blank import Blank
from .operations import Operation, OperationType, create_operation
from .path_generator import PathGenerator, ToolPath
from .optimization import PathOptimizer, OptimizationSettings

# Import tool_lib con gestione errori
try:
    from .tool_lib.tool_lib.milling import EndMill
    from .tool_lib.tool_lib.base import Tool
except ImportError:
    try:
        from tool_lib.tool_lib.milling import EndMill
        from tool_lib.tool_lib.base import Tool
    except ImportError:
        # Classi placeholder se tool_lib non disponibile
        class Tool:
            def __init__(self, name, diameter=None, length=None, code=None):
                self.name = name
                self.diameter = diameter
                self.length = length
                self.code = code

        class EndMill(Tool):
            def __init__(self, name, diameter, length, flutes=2, mill_type="flat", code=None):
                super().__init__(name, diameter, length, code)
                self.flutes = flutes
                self.mill_type = mill_type

# Import per superfici matematiche
from .operations import MathematicalSurface

class AxisConfiguration:
    def __init__(self, name: str, inverted: bool = False, mapped_to: str = None):
        """
        Configurazione di un singolo asse

        Args:
            name: Nome dell'asse (es. 'X', 'Y', 'Z', 'A', 'B', 'C')
            inverted: Se True, l'asse è invertito
            mapped_to: Se specificato, questo asse mappa a un altro asse standard
        """
        self.name = name
        self.inverted = inverted
        self.mapped_to = mapped_to if mapped_to else name

    def transform_value(self, value: float) -> float:
        """Trasforma un valore in base alla configurazione dell'asse"""
        return -value if self.inverted else value

    def __str__(self):
        inv = "invertito" if self.inverted else "normale"
        map_info = f" (mappato a {self.mapped_to})" if self.mapped_to != self.name else ""
        return f"Asse {self.name}{map_info}: {inv}"

class CoordinateMapper:
    def __init__(self):
        """Sistema per mappare le coordinate tra sistemi di riferimento diversi"""
        # Configurazione predefinita: X, Y, Z standard
        self.axes = {
            'X': AxisConfiguration('X'),
            'Y': AxisConfiguration('Y'),
            'Z': AxisConfiguration('Z')
        }
        self.rotary_axis = None

    def set_axis_config(self, axis_name: str, inverted: bool = False, mapped_to: str = None):
        """Configura un asse specifico"""
        self.axes[axis_name] = AxisConfiguration(axis_name, inverted, mapped_to)

    def add_rotary_axis(self, name: str, rotation_around: str = 'X', inverted: bool = False):
        """
        Aggiunge un asse rotativo

        Args:
            name: Nome dell'asse rotativo (tipicamente 'A', 'B', o 'C')
            rotation_around: Asse attorno al quale avviene la rotazione ('X', 'Y', o 'Z')
            inverted: Se True, il senso di rotazione è invertito
        """
        if rotation_around.upper() not in {'X', 'Y', 'Z'}:
            raise ValueError(f"Asse di rotazione non valido: {rotation_around}")
        if self.rotary_axis is not None:
            raise ValueError("È già presente un asse rotativo configurato")

        self.rotary_axis = {
            'name': name,
            'rotation_around': rotation_around.upper(),
            'inverted': inverted
        }
        self.axes[name] = AxisConfiguration(name, inverted)

    def transform_point(self, x: float, y: float, z: float, rotary_value: float = 0.0) -> Dict[str, float]:
        """
        Trasforma un punto dalle coordinate standard al sistema configurato

        Returns:
            Dizionario con le coordinate trasformate usando i nomi degli assi configurati
        """
        result = {}

        # Mappa le coordinate lineari
        standard_coords = {'X': x, 'Y': y, 'Z': z}

        for axis_name, config in self.axes.items():
            if axis_name == self.rotary_axis['name'] if self.rotary_axis else False:
                # Gestione dell'asse rotativo
                result[axis_name] = config.transform_value(rotary_value)
            elif config.mapped_to in standard_coords:
                # Gestione degli assi lineari
                result[axis_name] = config.transform_value(standard_coords[config.mapped_to])

        return result

    def __str__(self):
        axes_str = "\n".join(str(config) for config in self.axes.values())
        rotary_str = ""
        if self.rotary_axis:
            inv = "invertito" if self.rotary_axis['inverted'] else "normale"
            rotary_str = f"\nAsse rotativo {self.rotary_axis['name']} attorno a {self.rotary_axis['rotation_around']}: {inv}"
        return f"Configurazione assi:\n{axes_str}{rotary_str}"

class Rough:
    def __init__(self, shape: str,
                 dims: Optional[Tuple[float, float, float]] = None,
                 model_bbox: Optional[Tuple[float, float, float]] = None,
                 cylinder_axis: str = 'Z',
                 offsets: Optional[Dict[str, Tuple[float, float]]] = None):
        self.shape = shape.lower()
        self.dims = dims
        self.model_bbox = model_bbox
        if self.shape == 'cylinder' and cylinder_axis.upper() not in {'X', 'Y', 'Z'}:
            raise ValueError(f"Asse cilindro non valido: {cylinder_axis}")
        self.cylinder_axis = cylinder_axis.upper()
        default = {a: (0.0, 0.0) for a in ('X','Y','Z')}
        self.offsets = offsets or default
        self._compute_bbox()

    def _compute_bbox(self):
        if self.shape == 'cuboid' and self.dims:
            x, y, z = self.dims
            base = {'X': x, 'Y': y, 'Z': z}
        elif self.shape == 'cylinder' and self.dims and len(self.dims) == 2:
            d, h = self.dims[0], self.dims[1]
            if self.cylinder_axis == 'X':
                base = {'X': h, 'Y': d, 'Z': d}
            elif self.cylinder_axis == 'Y':
                base = {'X': d, 'Y': h, 'Z': d}
            else:
                base = {'X': d, 'Y': d, 'Z': h}
        elif self.shape == 'model' and self.model_bbox:
            base = dict(zip(('X','Y','Z'), self.model_bbox))
        else:
            raise ValueError(f"Parametri insufficienti per shape {self.shape}")
        self.bbox = {axis: (-off[0], base[axis] + off[1]) for axis, off in self.offsets.items()}

    def __str__(self):
        return f"Rough(shape={self.shape}, bbox={self.bbox})"

class CAMSetup:
    """
    Setup completo per lavorazioni CAM.

    Gestisce:
    - Grezzo (blank/stock)
    - Sistema di coordinate
    - Lavorazioni associate
    - Generazione e ottimizzazione percorsi
    """

    def __init__(self, name: str):
        """
        Inizializza un setup CAM.

        Args:
            name: Nome del setup
        """
        self.name = name
        self.blank: Optional[Blank] = None
        self.coordinate_system: Optional[CoordinateSystem] = None
        self.work_offset: Optional[WorkOffset] = None
        self.operations: List[Operation] = []
        self.model_surfaces: List[MathematicalSurface] = []

        # Generatori e ottimizzatori
        self.path_generator = PathGenerator()
        self.path_optimizer = PathOptimizer()
        self.optimization_settings = OptimizationSettings()

        # Risultati
        self.tool_paths: List[ToolPath] = []
        self.total_machining_time = 0.0

    def set_blank(self, blank: Blank):
        """Imposta il grezzo per questo setup."""
        self.blank = blank

    def add_model_surface(self, surface: MathematicalSurface):
        """Aggiunge una superficie del modello da lavorare."""
        self.model_surfaces.append(surface)

    def set_model_surfaces(self, surfaces: List[MathematicalSurface]):
        """Imposta le superfici del modello da lavorare."""
        self.model_surfaces = surfaces

    def set_coordinate_system(self, coord_system: CoordinateSystem, work_offset_number: int = 1):
        """
        Imposta il sistema di coordinate e work offset.

        Args:
            coord_system: Sistema di coordinate
            work_offset_number: Numero del work offset (1=G54, 2=G55, etc.)
        """
        self.coordinate_system = coord_system
        self.work_offset = coord_system.create_work_offset(work_offset_number, f"Setup {self.name}")
        self.path_generator.coordinate_system = coord_system

    def add_operation(self, operation: Operation):
        """Aggiunge una lavorazione al setup."""
        self.operations.append(operation)

    def create_operation(self, operation_type: OperationType, name: str,
                        tool: Tool, surfaces: List[MathematicalSurface] = None) -> Operation:
        """
        Crea e aggiunge una nuova lavorazione.

        Args:
            operation_type: Tipo di lavorazione
            name: Nome della lavorazione
            tool: Utensile da utilizzare
            surfaces: Superfici da lavorare (opzionale)

        Returns:
            Lavorazione creata
        """
        operation = create_operation(operation_type, name)
        operation.set_tool(tool)

        if surfaces:
            operation.add_surfaces(surfaces)

        self.add_operation(operation)
        return operation

    def generate_all_paths(self, enable_optimization: bool = True) -> List[ToolPath]:
        """
        Genera tutti i percorsi per le lavorazioni del setup.

        Args:
            enable_optimization: Se abilitare l'ottimizzazione

        Returns:
            Lista dei percorsi generati
        """
        self.tool_paths = []

        for operation in self.operations:
            if not operation.enabled:
                continue

            try:
                # Genera il percorso base
                tool_path = self.path_generator.generate_path(operation)

                # Ottimizza se richiesto
                if enable_optimization:
                    tool_path = self.path_optimizer.optimize_path(tool_path)

                self.tool_paths.append(tool_path)

            except Exception as e:
                print(f"Errore nella generazione del percorso per {operation.name}: {e}")

        # Calcola tempo totale
        self.total_machining_time = sum(path.calculate_time() for path in self.tool_paths)

        return self.tool_paths

    def get_operation_by_name(self, name: str) -> Optional[Operation]:
        """Trova una lavorazione per nome."""
        for operation in self.operations:
            if operation.name == name:
                return operation
        return None

    def remove_operation(self, name: str) -> bool:
        """
        Rimuove una lavorazione per nome.

        Args:
            name: Nome della lavorazione da rimuovere

        Returns:
            True se rimossa con successo
        """
        for i, operation in enumerate(self.operations):
            if operation.name == name:
                del self.operations[i]
                return True
        return False

    def validate_setup(self) -> Tuple[bool, List[str]]:
        """
        Valida la configurazione del setup.

        Returns:
            Tuple con (is_valid, error_messages)
        """
        errors = []

        if not self.blank:
            errors.append("Nessun grezzo specificato")

        if not self.coordinate_system:
            errors.append("Nessun sistema di coordinate specificato")

        if not self.operations:
            errors.append("Nessuna lavorazione specificata")

        # Valida ogni lavorazione
        for operation in self.operations:
            is_valid, op_errors = operation.validate()
            if not is_valid:
                errors.extend([f"{operation.name}: {error}" for error in op_errors])

        return len(errors) == 0, errors

    def get_setup_info(self) -> Dict[str, Any]:
        """Restituisce informazioni complete sul setup."""
        return {
            'name': self.name,
            'blank': {
                'name': self.blank.name if self.blank else None,
                'material': self.blank.material if self.blank else None,
                'type': type(self.blank).__name__ if self.blank else None
            },
            'coordinate_system': {
                'name': self.coordinate_system.name if self.coordinate_system else None,
                'origin': self.coordinate_system.origin if self.coordinate_system else None,
                'work_offset': self.work_offset.get_gcode_command() if self.work_offset else None
            },
            'operations': [
                {
                    'name': op.name,
                    'type': op.operation_type.value,
                    'tool': op.tool.name if op.tool else None,
                    'enabled': op.enabled,
                    'surfaces_count': len(op.surfaces)
                }
                for op in self.operations
            ],
            'paths_generated': len(self.tool_paths),
            'total_machining_time': self.total_machining_time
        }

    def export_gcode(self, file_path: str) -> bool:
        """
        Esporta il G-code per tutti i percorsi.

        Args:
            file_path: Percorso del file di output

        Returns:
            True se esportato con successo
        """
        if not self.tool_paths:
            return False

        try:
            from .gcode_generator import GCodeGenerator

            gcode_lines = []

            # Header
            gcode_lines.extend([
                f"; Setup: {self.name}",
                f"; Generated by CAM Library",
                f"; Total operations: {len(self.tool_paths)}",
                f"; Estimated time: {self.total_machining_time:.2f} minutes",
                "",
                "G21 ; Units in mm",
                "G90 ; Absolute positioning",
                "G17 ; XY plane selection",
            ])

            # Work offset
            if self.work_offset:
                gcode_lines.append(f"{self.work_offset.get_gcode_command()} ; Work offset")

            # Genera G-code per ogni percorso
            for i, tool_path in enumerate(self.tool_paths):
                gcode_lines.extend([
                    f"",
                    f"; Operation {i+1}: {tool_path.operation.name}",
                    f"; Tool: {tool_path.tool.name}",
                    f"M6 T{i+1} ; Tool change",
                    f"S{tool_path.operation.spindle_speed} M3 ; Spindle on",
                    f"F{tool_path.operation.feed_rate} ; Feed rate"
                ])

                # Converti percorso in G-code
                for point in tool_path.points:
                    if point.is_rapid:
                        gcode_lines.append(f"G0 X{point.x:.3f} Y{point.y:.3f} Z{point.z:.3f}")
                    else:
                        feed_cmd = f" F{point.feed_rate}" if point.feed_rate else ""
                        gcode_lines.append(f"G1 X{point.x:.3f} Y{point.y:.3f} Z{point.z:.3f}{feed_cmd}")

            # Footer
            gcode_lines.extend([
                "",
                "M5 ; Spindle off",
                "G0 Z50 ; Retract",
                "M30 ; Program end"
            ])

            # Salva file
            with open(file_path, 'w') as f:
                f.write('\n'.join(gcode_lines))

            return True

        except Exception as e:
            print(f"Errore nell'esportazione G-code: {e}")
            return False


# Mantieni la classe Setup originale per compatibilità
class Setup(CAMSetup):
    """Alias per compatibilità con il codice esistente."""
    pass