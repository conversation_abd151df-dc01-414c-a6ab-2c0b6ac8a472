"""
Modulo per la gestione dei sistemi di coordinate per lavorazioni CAM.

Supporta:
- Definizione di sistemi di coordinate personalizzati
- Posizionamento dell'origine
- Orientamento degli assi
- Trasformazioni tra sistemi di coordinate
- Gestione assi rotativi

Utilizza solo matematica pura senza dipendenze da librerie 3D.
"""

from typing import Dict, Tuple, Optional, List
from enum import Enum
import math


class AxisType(Enum):
    """Tipi di assi supportati."""
    LINEAR = "linear"
    ROTARY = "rotary"


class AxisDirection(Enum):
    """Direzioni standard degli assi."""
    POSITIVE_X = (1, 0, 0)
    NEGATIVE_X = (-1, 0, 0)
    POSITIVE_Y = (0, 1, 0)
    NEGATIVE_Y = (0, -1, 0)
    POSITIVE_Z = (0, 0, 1)
    NEGATIVE_Z = (0, 0, -1)


class AxisConfig:
    """Configurazione di un singolo asse."""

    def __init__(self, name: str, axis_type: AxisType, direction: Tuple[float, float, float],
                 inverted: bool = False, offset: float = 0.0, scale: float = 1.0):
        """
        Inizializza la configurazione di un asse.

        Args:
            name: Nome dell'asse (es. 'X', 'Y', 'Z', 'A', 'B', 'C')
            axis_type: Tipo di asse (lineare o rotativo)
            direction: Direzione dell'asse (vettore unitario)
            inverted: Se True, inverte la direzione
            offset: Offset dell'asse
            scale: Fattore di scala
        """
        self.name = name
        self.axis_type = axis_type
        self.direction = direction
        self.inverted = inverted
        self.offset = offset
        self.scale = scale

    def transform_value(self, value: float) -> float:
        """
        Trasforma un valore secondo la configurazione dell'asse.

        Args:
            value: Valore da trasformare

        Returns:
            Valore trasformato
        """
        result = value * self.scale + self.offset
        if self.inverted:
            result = -result
        return result

    def get_direction_vector(self) -> Tuple[float, float, float]:
        """Restituisce la direzione come vettore unitario."""
        direction = self.direction
        if self.inverted:
            direction = (-direction[0], -direction[1], -direction[2])
        return direction

    def normalize_vector(self, vector: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """Normalizza un vettore."""
        x, y, z = vector
        length = math.sqrt(x*x + y*y + z*z)
        if length == 0:
            return (0, 0, 0)
        return (x/length, y/length, z/length)


class CoordinateSystem:
    """Sistema di coordinate per lavorazioni CAM."""

    def __init__(self, name: str, origin: Tuple[float, float, float] = (0, 0, 0)):
        """
        Inizializza un sistema di coordinate.

        Args:
            name: Nome del sistema di coordinate
            origin: Punto di origine (x, y, z)
        """
        self.name = name
        self.origin = origin
        self.axes: Dict[str, AxisConfig] = {}
        self.rotary_axes: List[str] = []

        # Configurazione standard degli assi
        self._setup_standard_axes()

    def _setup_standard_axes(self):
        """Configura gli assi standard X, Y, Z."""
        self.axes['X'] = AxisConfig('X', AxisType.LINEAR, AxisDirection.POSITIVE_X.value)
        self.axes['Y'] = AxisConfig('Y', AxisType.LINEAR, AxisDirection.POSITIVE_Y.value)
        self.axes['Z'] = AxisConfig('Z', AxisType.LINEAR, AxisDirection.POSITIVE_Z.value)

    def add_axis(self, axis_config: AxisConfig):
        """
        Aggiunge un asse al sistema di coordinate.

        Args:
            axis_config: Configurazione dell'asse
        """
        self.axes[axis_config.name] = axis_config
        if axis_config.axis_type == AxisType.ROTARY:
            self.rotary_axes.append(axis_config.name)

    def add_rotary_axis(self, name: str, direction: Tuple[float, float, float],
                       inverted: bool = False, offset: float = 0.0):
        """
        Aggiunge un asse rotativo.

        Args:
            name: Nome dell'asse rotativo (es. 'A', 'B', 'C')
            direction: Direzione dell'asse di rotazione
            inverted: Se True, inverte la rotazione
            offset: Offset angolare in gradi
        """
        axis_config = AxisConfig(name, AxisType.ROTARY, direction, inverted, offset)
        self.add_axis(axis_config)

    def set_origin(self, x: float, y: float, z: float):
        """
        Imposta l'origine del sistema di coordinate.

        Args:
            x, y, z: Coordinate dell'origine
        """
        self.origin = (x, y, z)

    def set_axis_direction(self, axis_name: str, direction: Tuple[float, float, float]):
        """
        Modifica la direzione di un asse.

        Args:
            axis_name: Nome dell'asse
            direction: Nuova direzione
        """
        if axis_name in self.axes:
            self.axes[axis_name].direction = direction

    def invert_axis(self, axis_name: str):
        """
        Inverte la direzione di un asse.

        Args:
            axis_name: Nome dell'asse da invertire
        """
        if axis_name in self.axes:
            self.axes[axis_name].inverted = not self.axes[axis_name].inverted

    def transform_point(self, x: float, y: float, z: float,
                       rotary_values: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """
        Trasforma un punto dal sistema di coordinate standard al sistema configurato.

        Args:
            x, y, z: Coordinate del punto
            rotary_values: Valori degli assi rotativi

        Returns:
            Dizionario con le coordinate trasformate
        """
        if rotary_values is None:
            rotary_values = {}

        result = {}

        # Applica offset dell'origine
        x_offset = x - self.origin[0]
        y_offset = y - self.origin[1]
        z_offset = z - self.origin[2]

        # Trasforma gli assi lineari
        standard_coords = {'X': x_offset, 'Y': y_offset, 'Z': z_offset}

        for axis_name, axis_config in self.axes.items():
            if axis_config.axis_type == AxisType.LINEAR:
                # Trova la coordinata corrispondente
                coord_value = 0.0
                for std_axis, value in standard_coords.items():
                    if axis_config.direction == self.axes[std_axis].direction:
                        coord_value = value
                        break

                result[axis_name] = axis_config.transform_value(coord_value)

            elif axis_config.axis_type == AxisType.ROTARY:
                # Gestisce gli assi rotativi
                rotary_value = rotary_values.get(axis_name, 0.0)
                result[axis_name] = axis_config.transform_value(rotary_value)

        return result

    def get_transformation_matrix(self) -> List[List[float]]:
        """
        Calcola la matrice di trasformazione del sistema di coordinate.

        Returns:
            Matrice di trasformazione 4x4 come lista di liste
        """
        # Crea matrice identità 4x4
        matrix = [[1.0 if i == j else 0.0 for j in range(4)] for i in range(4)]

        # Applica traslazione dell'origine
        matrix[0][3] = -self.origin[0]
        matrix[1][3] = -self.origin[1]
        matrix[2][3] = -self.origin[2]

        # TODO: Implementare rotazioni degli assi se necessario

        return matrix

    def apply_transformation(self, point: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """
        Applica la trasformazione del sistema di coordinate a un punto.

        Args:
            point: Punto da trasformare (x, y, z)

        Returns:
            Punto trasformato
        """
        x, y, z = point

        # Applica traslazione dell'origine
        transformed_x = x - self.origin[0]
        transformed_y = y - self.origin[1]
        transformed_z = z - self.origin[2]

        # Applica trasformazioni degli assi
        result = {}
        for axis_name, axis_config in self.axes.items():
            if axis_config.axis_type == AxisType.LINEAR:
                if axis_name == 'X':
                    result['X'] = axis_config.transform_value(transformed_x)
                elif axis_name == 'Y':
                    result['Y'] = axis_config.transform_value(transformed_y)
                elif axis_name == 'Z':
                    result['Z'] = axis_config.transform_value(transformed_z)

        return (result.get('X', transformed_x),
                result.get('Y', transformed_y),
                result.get('Z', transformed_z))

    def create_work_offset(self, offset_number: int, description: str = "") -> 'WorkOffset':
        """
        Crea un work offset basato su questo sistema di coordinate.

        Args:
            offset_number: Numero del work offset (es. G54=1, G55=2, etc.)
            description: Descrizione del work offset

        Returns:
            Istanza di WorkOffset
        """
        return WorkOffset(offset_number, self, description)


class WorkOffset:
    """Rappresenta un work offset (G54, G55, etc.) per macchine CNC."""

    def __init__(self, number: int, coordinate_system: CoordinateSystem, description: str = ""):
        """
        Inizializza un work offset.

        Args:
            number: Numero del work offset (1=G54, 2=G55, etc.)
            coordinate_system: Sistema di coordinate associato
            description: Descrizione del work offset
        """
        self.number = number
        self.coordinate_system = coordinate_system
        self.description = description

    def get_gcode_command(self) -> str:
        """
        Restituisce il comando G-code per attivare questo work offset.

        Returns:
            Comando G-code (es. "G54")
        """
        if 1 <= self.number <= 6:
            return f"G{53 + self.number}"
        else:
            return f"G59.{self.number - 6}"

    def get_offset_values(self) -> Dict[str, float]:
        """
        Calcola i valori di offset per ogni asse.

        Returns:
            Dizionario con i valori di offset
        """
        offsets = {}
        for axis_name, axis_config in self.coordinate_system.axes.items():
            if axis_config.axis_type == AxisType.LINEAR:
                # Per gli assi lineari, l'offset è la posizione dell'origine
                if axis_name == 'X':
                    offsets[axis_name] = self.coordinate_system.origin[0]
                elif axis_name == 'Y':
                    offsets[axis_name] = self.coordinate_system.origin[1]
                elif axis_name == 'Z':
                    offsets[axis_name] = self.coordinate_system.origin[2]

        return offsets


def create_standard_coordinate_systems() -> Dict[str, CoordinateSystem]:
    """
    Crea una collezione di sistemi di coordinate standard.

    Returns:
        Dizionario con sistemi di coordinate predefiniti
    """
    systems = {}

    # Sistema standard
    systems['Standard'] = CoordinateSystem('Standard')

    # Sistema per tornitura (X=diametro, Z=lunghezza)
    turning_system = CoordinateSystem('Turning')
    turning_system.set_axis_direction('X', AxisDirection.POSITIVE_X.value)  # Diametro
    turning_system.set_axis_direction('Z', AxisDirection.POSITIVE_Z.value)  # Lunghezza
    systems['Turning'] = turning_system

    # Sistema per fresatura 4 assi (con asse A)
    milling_4axis = CoordinateSystem('Milling_4Axis')
    milling_4axis.add_rotary_axis('A', AxisDirection.POSITIVE_X.value)
    systems['Milling_4Axis'] = milling_4axis

    # Sistema per fresatura 5 assi (con assi A e B)
    milling_5axis = CoordinateSystem('Milling_5Axis')
    milling_5axis.add_rotary_axis('A', AxisDirection.POSITIVE_X.value)
    milling_5axis.add_rotary_axis('B', AxisDirection.POSITIVE_Y.value)
    systems['Milling_5Axis'] = milling_5axis

    return systems
