"""
Test script per l'ottimizzazione dei percorsi CAM con visualizzazione PyVista.

Questo script testa:
1. Ottimizzazione percorsi
2. Riduzione punti
3. Fitting archi
4. Confronto prima/dopo ottimizzazione
"""

import sys
import os
# Aggiungi il percorso della libreria principale
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pyvista as pv
import numpy as np
from optimization import PathOptimizer, OptimizationSettings
from path_generator import PathGenerator, PathPoint, ToolPath
from operations import RoughingOperation, MathematicalSurface


# Classe placeholder per Tool se non disponibile
class Tool:
    def __init__(self, name, diameter, length, code=None):
        self.name = name
        self.diameter = diameter
        self.length = length
        self.code = code


def create_test_path():
    """Crea un percorso di test con molti punti."""
    # Crea superficie
    surface = MathematicalSurface('plane', {
        'center': (0, 0, 0),
        'size': (40, 30),
        'normal': (0, 0, 1)
    })
    
    # Crea operazione
    tool = Tool("Fresa_Test", diameter=6.0, length=40.0)
    operation = RoughingOperation("Test_Path")
    operation.set_tool(tool)
    operation.add_surface(surface)
    operation.set_speeds_and_feeds(2000, 250, 50)
    
    # Crea percorso con molti punti (simula zigzag denso)
    points = []
    
    # Pattern zigzag
    x_start, x_end = -15, 15
    y_start, y_end = -10, 10
    z_work = -1.0
    
    num_passes = 15
    points_per_pass = 20
    
    for i in range(num_passes):
        y = y_start + (i / (num_passes - 1)) * (y_end - y_start)
        
        if i % 2 == 0:  # Passata da sinistra a destra
            x_range = np.linspace(x_start, x_end, points_per_pass)
        else:  # Passata da destra a sinistra
            x_range = np.linspace(x_end, x_start, points_per_pass)
        
        for x in x_range:
            # Aggiungi piccole variazioni per simulare imperfezioni
            noise_x = np.random.normal(0, 0.1)
            noise_y = np.random.normal(0, 0.1)
            noise_z = np.random.normal(0, 0.05)
            
            point = PathPoint(
                x + noise_x,
                y + noise_y, 
                z_work + noise_z,
                feed_rate=operation.feed_rate
            )
            points.append(point)
    
    # Crea ToolPath
    tool_path = ToolPath(operation, tool)
    tool_path.points = points
    tool_path._calculate_properties()
    
    return tool_path


def visualize_path_comparison(plotter, original_path, optimized_path, title="Confronto Percorsi"):
    """Visualizza confronto tra percorso originale e ottimizzato."""
    
    # Percorso originale
    if original_path and original_path.points:
        orig_points = np.array([[p.x, p.y, p.z] for p in original_path.points])
        if len(orig_points) > 1:
            orig_spline = pv.Spline(orig_points)
            plotter.add_mesh(orig_spline, color='red', line_width=2, label='Originale')
            plotter.add_points(orig_points[::10], color='red', point_size=4)  # Ogni 10° punto
    
    # Percorso ottimizzato
    if optimized_path and optimized_path.points:
        opt_points = np.array([[p.x, p.y, p.z] for p in optimized_path.points])
        if len(opt_points) > 1:
            opt_spline = pv.Spline(opt_points)
            plotter.add_mesh(opt_spline, color='blue', line_width=3, label='Ottimizzato')
            plotter.add_points(opt_points, color='blue', point_size=6)


def test_point_reduction():
    """Test riduzione punti."""
    print("=== Test Riduzione Punti ===")
    
    # Crea percorso di test
    original_path = create_test_path()
    print(f"Percorso originale: {len(original_path.points)} punti")
    print(f"Lunghezza originale: {original_path.total_length:.2f} mm")
    
    # Configura ottimizzatore
    optimizer = PathOptimizer()
    settings = OptimizationSettings()
    settings.enable_point_reduction = True
    settings.enable_arc_fitting = False  # Solo riduzione punti
    settings.point_reduction_tolerance = 0.1
    
    # Ottimizza
    optimized_path = optimizer.optimize_path(original_path, settings)
    
    print(f"Percorso ottimizzato: {len(optimized_path.points)} punti")
    print(f"Lunghezza ottimizzata: {optimized_path.total_length:.2f} mm")
    print(f"Riduzione punti: {((len(original_path.points) - len(optimized_path.points)) / len(original_path.points) * 100):.1f}%")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Test Riduzione Punti")
    
    visualize_path_comparison(plotter, original_path, optimized_path)
    
    # Statistiche
    stats_text = f"""Riduzione Punti:
Punti originali: {len(original_path.points)}
Punti ottimizzati: {len(optimized_path.points)}
Riduzione: {((len(original_path.points) - len(optimized_path.points)) / len(original_path.points) * 100):.1f}%
Lunghezza orig: {original_path.total_length:.2f} mm
Lunghezza ott: {optimized_path.total_length:.2f} mm
Tolleranza: {settings.point_reduction_tolerance} mm"""
    
    plotter.add_text(stats_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return original_path, optimized_path


def test_arc_fitting():
    """Test fitting archi."""
    print("\n=== Test Fitting Archi ===")
    
    # Crea percorso con curve (cerchio)
    tool = Tool("Fresa_Test", diameter=4.0, length=30.0)
    operation = RoughingOperation("Arc_Test")
    operation.set_tool(tool)
    operation.set_speeds_and_feeds(2000, 200, 40)
    
    # Crea percorso circolare con molti punti
    points = []
    center = (0, 0, -1)
    radius = 15
    num_points = 50
    
    for i in range(num_points + 1):  # +1 per chiudere il cerchio
        angle = (i / num_points) * 2 * np.pi
        x = center[0] + radius * np.cos(angle)
        y = center[1] + radius * np.sin(angle)
        z = center[2]
        
        point = PathPoint(x, y, z, feed_rate=operation.feed_rate)
        points.append(point)
    
    original_path = ToolPath(operation, tool)
    original_path.points = points
    original_path._calculate_properties()
    
    print(f"Percorso circolare originale: {len(original_path.points)} punti")
    
    # Configura ottimizzatore per arc fitting
    optimizer = PathOptimizer()
    settings = OptimizationSettings()
    settings.enable_point_reduction = False
    settings.enable_arc_fitting = True
    settings.chord_tolerance = 0.1
    settings.min_arc_length = 5.0
    
    # Ottimizza
    optimized_path = optimizer.optimize_path(original_path, settings)
    
    print(f"Percorso con archi: {len(optimized_path.points)} punti")
    print(f"Archi identificati: {len([p for p in optimized_path.points if hasattr(p, 'is_arc') and p.is_arc])}")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Test Fitting Archi")
    
    visualize_path_comparison(plotter, original_path, optimized_path)
    
    # Cerchio di riferimento
    circle = pv.Circle(radius=radius, center=center)
    plotter.add_mesh(circle, color='green', style='wireframe', line_width=1, label='Cerchio Ideale')
    
    # Statistiche
    stats_text = f"""Fitting Archi:
Punti originali: {len(original_path.points)}
Punti ottimizzati: {len(optimized_path.points)}
Riduzione: {((len(original_path.points) - len(optimized_path.points)) / len(original_path.points) * 100):.1f}%
Tolleranza corda: {settings.chord_tolerance} mm
Lunghezza min arco: {settings.min_arc_length} mm"""
    
    plotter.add_text(stats_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return original_path, optimized_path


def test_complete_optimization():
    """Test ottimizzazione completa."""
    print("\n=== Test Ottimizzazione Completa ===")
    
    # Crea percorso complesso
    original_path = create_test_path()
    
    # Configura ottimizzazione completa
    optimizer = PathOptimizer()
    settings = OptimizationSettings()
    settings.enable_point_reduction = True
    settings.enable_arc_fitting = True
    settings.enable_speed_optimization = True
    settings.point_reduction_tolerance = 0.05
    settings.chord_tolerance = 0.1
    settings.min_arc_length = 3.0
    
    print(f"Percorso originale: {len(original_path.points)} punti, {original_path.total_length:.2f} mm")
    print(f"Tempo originale: {original_path.machining_time:.2f} min")
    
    # Ottimizza
    optimized_path = optimizer.optimize_path(original_path, settings)
    
    print(f"Percorso ottimizzato: {len(optimized_path.points)} punti, {optimized_path.total_length:.2f} mm")
    print(f"Tempo ottimizzato: {optimized_path.machining_time:.2f} min")
    
    # Calcola miglioramenti
    point_reduction = ((len(original_path.points) - len(optimized_path.points)) / len(original_path.points) * 100)
    time_reduction = ((original_path.machining_time - optimized_path.machining_time) / original_path.machining_time * 100)
    
    print(f"Riduzione punti: {point_reduction:.1f}%")
    print(f"Riduzione tempo: {time_reduction:.1f}%")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Ottimizzazione Completa")
    
    visualize_path_comparison(plotter, original_path, optimized_path)
    
    # Superficie di riferimento
    surface_mesh = pv.Plane(center=(0, 0, -1), i_size=40, j_size=30)
    plotter.add_mesh(surface_mesh, color='lightgray', opacity=0.3, label='Superficie')
    
    # Statistiche complete
    stats_text = f"""Ottimizzazione Completa:
Punti: {len(original_path.points)} → {len(optimized_path.points)} (-{point_reduction:.1f}%)
Lunghezza: {original_path.total_length:.1f} → {optimized_path.total_length:.1f} mm
Tempo: {original_path.machining_time:.2f} → {optimized_path.machining_time:.2f} min (-{time_reduction:.1f}%)

Impostazioni:
• Riduzione punti: {settings.enable_point_reduction}
• Fitting archi: {settings.enable_arc_fitting}
• Ottimizzazione velocità: {settings.enable_speed_optimization}
• Tolleranza: {settings.point_reduction_tolerance} mm"""
    
    plotter.add_text(stats_text, position='upper_left', font_size=9)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return original_path, optimized_path


def main():
    """Esegue tutti i test."""
    print("Test Ottimizzazione Percorsi CAM con Visualizzazione PyVista")
    print("=" * 60)
    
    try:
        # Test 1: Riduzione punti
        orig1, opt1 = test_point_reduction()
        
        # Test 2: Fitting archi
        orig2, opt2 = test_arc_fitting()
        
        # Test 3: Ottimizzazione completa
        orig3, opt3 = test_complete_optimization()
        
        print("\n" + "=" * 60)
        print("Tutti i test completati con successo!")
        
        # Riepilogo miglioramenti
        print("\nRiepilogo miglioramenti:")
        tests = [
            ("Riduzione Punti", orig1, opt1),
            ("Fitting Archi", orig2, opt2), 
            ("Ottimizzazione Completa", orig3, opt3)
        ]
        
        for name, orig, opt in tests:
            if orig and opt:
                point_reduction = ((len(orig.points) - len(opt.points)) / len(orig.points) * 100)
                print(f"  {name}: -{point_reduction:.1f}% punti")
            
    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
