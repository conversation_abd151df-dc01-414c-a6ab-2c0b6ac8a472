"""
Modulo per l'ottimizzazione dei percorsi CAM.

Supporta:
- Ottimizzazione degli spostamenti
- Conversione linee in archi/raggi
- Riduzione punti ridondanti
- Riordinamento percorsi per efficienza
- Controllo tolleranze e precisione
"""

from typing import List, Tuple, Optional, Dict, Any
import numpy as np
import math
from .path_generator import PathPoint, ToolPath


class OptimizationSettings:
    """Impostazioni per l'ottimizzazione dei percorsi."""
    
    def __init__(self):
        """Inizializza le impostazioni di ottimizzazione."""
        # Tolleranze
        self.chord_tolerance = 0.01      # mm - tolleranza massima dalla corda per archi
        self.point_tolerance = 0.001     # mm - tolleranza per eliminazione punti ridondanti
        self.angle_tolerance = 1.0       # gradi - tolleranza angolare per linearità
        
        # Ottimizzazioni abilitate
        self.enable_arc_fitting = True   # Converti linee in archi
        self.enable_point_reduction = True  # Rimuovi punti ridondanti
        self.enable_path_reordering = True  # Riordina percorsi per efficienza
        self.enable_rapid_optimization = True  # Ottimizza movimenti rapidi
        
        # Parametri archi
        self.min_arc_angle = 5.0         # gradi - angolo minimo per considerare un arco
        self.max_arc_angle = 180.0       # gradi - angolo massimo per un singolo arco
        self.min_arc_radius = 0.1        # mm - raggio minimo per archi
        self.max_arc_radius = 1000.0     # mm - raggio massimo per archi
        
        # Parametri riduzione punti
        self.min_segment_length = 0.01   # mm - lunghezza minima segmento
        self.max_deviation = 0.005       # mm - deviazione massima per eliminazione punti


class ArcSegment:
    """Rappresenta un segmento ad arco."""
    
    def __init__(self, start: PathPoint, end: PathPoint, center: Tuple[float, float], 
                 radius: float, clockwise: bool = True):
        """
        Inizializza un segmento ad arco.
        
        Args:
            start: Punto iniziale
            end: Punto finale
            center: Centro dell'arco (x, y)
            radius: Raggio dell'arco
            clockwise: Direzione dell'arco (True = orario)
        """
        self.start = start
        self.end = end
        self.center = center
        self.radius = radius
        self.clockwise = clockwise
        self.angle = self._calculate_angle()
    
    def _calculate_angle(self) -> float:
        """Calcola l'angolo dell'arco in gradi."""
        start_angle = math.atan2(self.start.y - self.center[1], self.start.x - self.center[0])
        end_angle = math.atan2(self.end.y - self.center[1], self.end.x - self.center[0])
        
        angle_diff = end_angle - start_angle
        if self.clockwise:
            if angle_diff > 0:
                angle_diff -= 2 * math.pi
        else:
            if angle_diff < 0:
                angle_diff += 2 * math.pi
        
        return abs(math.degrees(angle_diff))
    
    def get_intermediate_points(self, num_points: int) -> List[PathPoint]:
        """Genera punti intermedi lungo l'arco."""
        points = []
        start_angle = math.atan2(self.start.y - self.center[1], self.start.x - self.center[0])
        end_angle = math.atan2(self.end.y - self.center[1], self.end.x - self.center[0])
        
        angle_diff = end_angle - start_angle
        if self.clockwise:
            if angle_diff > 0:
                angle_diff -= 2 * math.pi
        else:
            if angle_diff < 0:
                angle_diff += 2 * math.pi
        
        for i in range(1, num_points + 1):
            t = i / (num_points + 1)
            angle = start_angle + t * angle_diff
            
            x = self.center[0] + self.radius * math.cos(angle)
            y = self.center[1] + self.radius * math.sin(angle)
            z = self.start.z + t * (self.end.z - self.start.z)  # Interpolazione lineare in Z
            
            points.append(PathPoint(x, y, z, feed_rate=self.start.feed_rate))
        
        return points


class PathOptimizer:
    """Ottimizzatore principale per percorsi CAM."""
    
    def __init__(self, settings: Optional[OptimizationSettings] = None):
        """
        Inizializza l'ottimizzatore.
        
        Args:
            settings: Impostazioni di ottimizzazione
        """
        self.settings = settings or OptimizationSettings()
    
    def optimize_path(self, tool_path: ToolPath) -> ToolPath:
        """
        Ottimizza un percorso completo.
        
        Args:
            tool_path: Percorso da ottimizzare
            
        Returns:
            Percorso ottimizzato
        """
        optimized_path = ToolPath(tool_path.operation, tool_path.tool)
        
        # Ottimizza ogni segmento
        for segment in tool_path.segments:
            optimized_segment = self.optimize_segment(segment)
            if optimized_segment:
                optimized_path.add_segment(optimized_segment)
        
        # Ottimizzazioni globali
        if self.settings.enable_path_reordering:
            optimized_path = self._reorder_segments(optimized_path)
        
        if self.settings.enable_rapid_optimization:
            optimized_path = self._optimize_rapid_moves(optimized_path)
        
        # Ricalcola metriche
        optimized_path.calculate_length()
        optimized_path.calculate_time()
        
        return optimized_path
    
    def optimize_segment(self, segment: List[PathPoint]) -> List[PathPoint]:
        """
        Ottimizza un singolo segmento.
        
        Args:
            segment: Segmento da ottimizzare
            
        Returns:
            Segmento ottimizzato
        """
        if len(segment) < 2:
            return segment
        
        optimized = segment.copy()
        
        # Riduzione punti ridondanti
        if self.settings.enable_point_reduction:
            optimized = self._reduce_points(optimized)
        
        # Conversione in archi
        if self.settings.enable_arc_fitting:
            optimized = self._fit_arcs(optimized)
        
        return optimized
    
    def _reduce_points(self, points: List[PathPoint]) -> List[PathPoint]:
        """Rimuove punti ridondanti da un segmento."""
        if len(points) < 3:
            return points
        
        reduced = [points[0]]  # Mantieni sempre il primo punto
        
        i = 1
        while i < len(points) - 1:
            current = points[i]
            prev = reduced[-1]
            next_point = points[i + 1]
            
            # Verifica se il punto corrente può essere rimosso
            if self._can_remove_point(prev, current, next_point):
                i += 1  # Salta il punto corrente
            else:
                reduced.append(current)
                i += 1
        
        reduced.append(points[-1])  # Mantieni sempre l'ultimo punto
        return reduced
    
    def _can_remove_point(self, prev: PathPoint, current: PathPoint, next_point: PathPoint) -> bool:
        """Verifica se un punto può essere rimosso senza perdere precisione."""
        # Calcola la distanza del punto dalla linea che connette prev e next
        deviation = self._point_to_line_distance(current, prev, next_point)
        
        # Verifica anche l'angolo
        angle = self._calculate_angle(prev, current, next_point)
        
        return (deviation < self.settings.max_deviation and 
                abs(180 - angle) < self.settings.angle_tolerance)
    
    def _point_to_line_distance(self, point: PathPoint, line_start: PathPoint, line_end: PathPoint) -> float:
        """Calcola la distanza di un punto da una linea."""
        # Vettore della linea
        line_vec = np.array([line_end.x - line_start.x, line_end.y - line_start.y, line_end.z - line_start.z])
        # Vettore dal punto iniziale al punto
        point_vec = np.array([point.x - line_start.x, point.y - line_start.y, point.z - line_start.z])
        
        # Lunghezza della linea
        line_length = np.linalg.norm(line_vec)
        if line_length == 0:
            return np.linalg.norm(point_vec)
        
        # Proiezione del punto sulla linea
        projection = np.dot(point_vec, line_vec) / line_length
        projection_vec = (projection / line_length) * line_vec
        
        # Distanza perpendicolare
        perpendicular = point_vec - projection_vec
        return np.linalg.norm(perpendicular)
    
    def _calculate_angle(self, p1: PathPoint, p2: PathPoint, p3: PathPoint) -> float:
        """Calcola l'angolo tra tre punti in gradi."""
        v1 = np.array([p2.x - p1.x, p2.y - p1.y])
        v2 = np.array([p3.x - p2.x, p3.y - p2.y])
        
        # Normalizza i vettori
        v1_norm = np.linalg.norm(v1)
        v2_norm = np.linalg.norm(v2)
        
        if v1_norm == 0 or v2_norm == 0:
            return 180.0
        
        v1_unit = v1 / v1_norm
        v2_unit = v2 / v2_norm
        
        # Calcola l'angolo
        dot_product = np.clip(np.dot(v1_unit, v2_unit), -1.0, 1.0)
        angle = math.degrees(math.acos(dot_product))
        
        return angle
    
    def _fit_arcs(self, points: List[PathPoint]) -> List[PathPoint]:
        """Converte sequenze di punti lineari in archi quando possibile."""
        if len(points) < 3:
            return points
        
        optimized = []
        i = 0
        
        while i < len(points):
            # Cerca il segmento più lungo che può essere convertito in arco
            arc_segment = self._find_arc_segment(points, i)
            
            if arc_segment and len(arc_segment) >= 3:
                # Converti in arco
                arc = self._create_arc_from_points(arc_segment)
                if arc:
                    # Aggiungi punto iniziale e finale dell'arco
                    optimized.append(arc_segment[0])
                    optimized.append(arc_segment[-1])
                    i += len(arc_segment)
                else:
                    # Se non riesce a creare l'arco, aggiungi il punto corrente
                    optimized.append(points[i])
                    i += 1
            else:
                # Aggiungi il punto corrente
                optimized.append(points[i])
                i += 1
        
        return optimized
    
    def _find_arc_segment(self, points: List[PathPoint], start_idx: int) -> Optional[List[PathPoint]]:
        """Trova il segmento più lungo che può essere convertito in arco."""
        if start_idx + 2 >= len(points):
            return None
        
        # Inizia con tre punti
        segment = points[start_idx:start_idx + 3]
        
        # Estendi il segmento finché rimane un arco valido
        for end_idx in range(start_idx + 3, len(points)):
            test_segment = points[start_idx:end_idx + 1]
            
            if self._is_valid_arc_segment(test_segment):
                segment = test_segment
            else:
                break
        
        return segment if len(segment) >= 3 else None
    
    def _is_valid_arc_segment(self, points: List[PathPoint]) -> bool:
        """Verifica se una sequenza di punti può formare un arco valido."""
        if len(points) < 3:
            return False
        
        # Calcola il centro e raggio dell'arco
        center, radius = self._calculate_arc_center_radius(points[0], points[1], points[2])
        
        if not center or not radius:
            return False
        
        # Verifica i vincoli del raggio
        if radius < self.settings.min_arc_radius or radius > self.settings.max_arc_radius:
            return False
        
        # Verifica che tutti i punti siano vicini al cerchio
        for point in points:
            distance_to_center = math.sqrt((point.x - center[0])**2 + (point.y - center[1])**2)
            deviation = abs(distance_to_center - radius)
            
            if deviation > self.settings.chord_tolerance:
                return False
        
        return True
    
    def _calculate_arc_center_radius(self, p1: PathPoint, p2: PathPoint, p3: PathPoint) -> Tuple[Optional[Tuple[float, float]], Optional[float]]:
        """Calcola centro e raggio di un arco passante per tre punti."""
        # Coordinate dei punti
        x1, y1 = p1.x, p1.y
        x2, y2 = p2.x, p2.y
        x3, y3 = p3.x, p3.y
        
        # Calcola il centro del cerchio
        d = 2 * (x1 * (y2 - y3) + x2 * (y3 - y1) + x3 * (y1 - y2))
        
        if abs(d) < 1e-10:  # Punti collineari
            return None, None
        
        ux = ((x1**2 + y1**2) * (y2 - y3) + (x2**2 + y2**2) * (y3 - y1) + (x3**2 + y3**2) * (y1 - y2)) / d
        uy = ((x1**2 + y1**2) * (x3 - x2) + (x2**2 + y2**2) * (x1 - x3) + (x3**2 + y3**2) * (x2 - x1)) / d
        
        # Calcola il raggio
        radius = math.sqrt((x1 - ux)**2 + (y1 - uy)**2)
        
        return (ux, uy), radius
    
    def _create_arc_from_points(self, points: List[PathPoint]) -> Optional[ArcSegment]:
        """Crea un segmento ad arco da una sequenza di punti."""
        if len(points) < 3:
            return None
        
        center, radius = self._calculate_arc_center_radius(points[0], points[1], points[2])
        
        if not center or not radius:
            return None
        
        # Determina la direzione dell'arco
        clockwise = self._is_clockwise(points[0], points[1], points[2])
        
        return ArcSegment(points[0], points[-1], center, radius, clockwise)
    
    def _is_clockwise(self, p1: PathPoint, p2: PathPoint, p3: PathPoint) -> bool:
        """Determina se tre punti formano una curva in senso orario."""
        cross_product = (p2.x - p1.x) * (p3.y - p1.y) - (p2.y - p1.y) * (p3.x - p1.x)
        return cross_product < 0
    
    def _reorder_segments(self, tool_path: ToolPath) -> ToolPath:
        """Riordina i segmenti per minimizzare i movimenti rapidi."""
        if len(tool_path.segments) <= 1:
            return tool_path
        
        # Implementazione semplificata: ordina per vicinanza
        reordered_path = ToolPath(tool_path.operation, tool_path.tool)
        remaining_segments = tool_path.segments.copy()
        
        # Inizia dal primo segmento
        current_segment = remaining_segments.pop(0)
        reordered_path.add_segment(current_segment)
        current_end = current_segment[-1]
        
        # Trova il segmento più vicino
        while remaining_segments:
            min_distance = float('inf')
            closest_idx = 0
            
            for i, segment in enumerate(remaining_segments):
                distance = current_end.distance_to(segment[0])
                if distance < min_distance:
                    min_distance = distance
                    closest_idx = i
            
            next_segment = remaining_segments.pop(closest_idx)
            reordered_path.add_segment(next_segment)
            current_end = next_segment[-1]
        
        return reordered_path
    
    def _optimize_rapid_moves(self, tool_path: ToolPath) -> ToolPath:
        """Ottimizza i movimenti rapidi tra segmenti."""
        # Implementazione semplificata: aggiunge movimenti rapidi espliciti
        optimized_path = ToolPath(tool_path.operation, tool_path.tool)
        
        for i, segment in enumerate(tool_path.segments):
            if i > 0:
                # Aggiungi movimento rapido dal segmento precedente
                prev_end = tool_path.segments[i-1][-1]
                current_start = segment[0]
                
                # Movimento rapido in Z
                rapid_up = PathPoint(prev_end.x, prev_end.y, tool_path.operation.safe_height, is_rapid=True)
                rapid_move = PathPoint(current_start.x, current_start.y, tool_path.operation.safe_height, is_rapid=True)
                rapid_down = PathPoint(current_start.x, current_start.y, current_start.z, is_rapid=True)
                
                optimized_path.add_segment([rapid_up, rapid_move, rapid_down])
            
            optimized_path.add_segment(segment)
        
        return optimized_path
    
    def get_optimization_report(self, original_path: ToolPath, optimized_path: ToolPath) -> Dict[str, Any]:
        """Genera un report delle ottimizzazioni applicate."""
        original_length = original_path.calculate_length()
        optimized_length = optimized_path.calculate_length()
        
        original_time = original_path.calculate_time()
        optimized_time = optimized_path.calculate_time()
        
        return {
            'original_points': len(original_path.points),
            'optimized_points': len(optimized_path.points),
            'points_reduction': len(original_path.points) - len(optimized_path.points),
            'original_length': original_length,
            'optimized_length': optimized_length,
            'length_reduction': original_length - optimized_length,
            'original_time': original_time,
            'optimized_time': optimized_time,
            'time_reduction': original_time - optimized_time,
            'optimization_percentage': ((original_time - optimized_time) / original_time * 100) if original_time > 0 else 0
        }
