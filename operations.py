"""
Modulo per la gestione delle lavorazioni CAM.

Supporta diversi tipi di lavorazioni:
- Sgrossatura (Roughing)
- Contornatura (Contouring)
- Incisione (Engraving)
- Foratura (Drilling)
- Maschiatura (Tapping)

Ogni lavorazione ha parametri specifici e può essere associata a superfici matematiche e utensili.
Utilizza solo matematica pura senza dipendenze da librerie 3D.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
import math

# Rappresentazione matematica di una superficie
class MathematicalSurface:
    """Rappresentazione matematica di una superficie per lavorazioni CAM."""

    def __init__(self, surface_type: str, parameters: Dict[str, Any]):
        """
        Inizializza una superficie matematica.

        Args:
            surface_type: Tipo di superficie ('plane', 'cylinder', 'sphere', 'custom')
            parameters: Parametri specifici della superficie
        """
        self.surface_type = surface_type
        self.parameters = parameters

    def get_bounding_box(self) -> <PERSON><PERSON>[Tuple[float, float, float], Tuple[float, float, float]]:
        """Calcola il bounding box della superficie."""
        if 'bbox' in self.parameters:
            return self.parameters['bbox']

        # Calcolo di default per superfici piane
        if self.surface_type == 'plane':
            center = self.parameters.get('center', (0, 0, 0))
            size = self.parameters.get('size', (10, 10))
            min_point = (center[0] - size[0]/2, center[1] - size[1]/2, center[2])
            max_point = (center[0] + size[0]/2, center[1] + size[1]/2, center[2])
            return (min_point, max_point)

        return ((0, 0, 0), (10, 10, 10))

    def get_area(self) -> float:
        """Calcola l'area della superficie."""
        if self.surface_type == 'plane':
            size = self.parameters.get('size', (10, 10))
            return size[0] * size[1]
        elif self.surface_type == 'cylinder':
            radius = self.parameters.get('radius', 1)
            height = self.parameters.get('height', 1)
            return 2 * math.pi * radius * height
        else:
            return self.parameters.get('area', 100.0)

# Import tool_lib con gestione errori
try:
    from .tool_lib.tool_lib.base import Tool
except ImportError:
    try:
        from tool_lib.tool_lib.base import Tool
    except ImportError:
        # Classe placeholder se tool_lib non disponibile
        class Tool:
            def __init__(self, name, diameter=None, length=None, code=None):
                self.name = name
                self.diameter = diameter
                self.length = length
                self.code = code


class OperationType(Enum):
    """Tipi di lavorazioni supportate."""
    ROUGHING = "roughing"
    CONTOURING = "contouring"
    ENGRAVING = "engraving"
    DRILLING = "drilling"
    TAPPING = "tapping"
    POCKETING = "pocketing"
    FACING = "facing"


class CuttingDirection(Enum):
    """Direzioni di taglio."""
    CONVENTIONAL = "conventional"  # Concordanza
    CLIMB = "climb"               # Discordanza


class Operation(ABC):
    """Classe base astratta per tutte le lavorazioni."""

    def __init__(self, name: str, operation_type: OperationType):
        """
        Inizializza una lavorazione base.

        Args:
            name: Nome della lavorazione
            operation_type: Tipo di lavorazione
        """
        self.name = name
        self.operation_type = operation_type
        self.tool: Optional[Tool] = None
        self.surfaces: List[MathematicalSurface] = []
        self.enabled = True

        # Parametri comuni
        self.spindle_speed = 1000  # RPM
        self.feed_rate = 100       # mm/min
        self.plunge_rate = 50      # mm/min
        self.safe_height = 10.0    # mm
        self.clearance_height = 5.0 # mm
        self.cutting_direction = CuttingDirection.CLIMB

        # Parametri di precisione
        self.tolerance = 0.01      # mm
        self.step_down = 1.0       # mm per passata
        self.step_over = 0.5       # mm tra passate

    def set_tool(self, tool: Tool):
        """Imposta l'utensile per questa lavorazione."""
        self.tool = tool

    def add_surface(self, surface: MathematicalSurface):
        """Aggiunge una superficie da lavorare."""
        self.surfaces.append(surface)

    def add_surfaces(self, surfaces: List[MathematicalSurface]):
        """Aggiunge multiple superfici da lavorare."""
        self.surfaces.extend(surfaces)

    # Mantieni compatibilità con il vecchio API
    def add_face(self, face):
        """Aggiunge una faccia da lavorare (compatibilità)."""
        # Converte automaticamente in superficie matematica
        if hasattr(face, 'surface_type'):
            self.add_surface(face)
        else:
            # Crea una superficie piana di default
            surface = MathematicalSurface('plane', {'center': (0, 0, 0), 'size': (10, 10)})
            self.add_surface(surface)

    def add_faces(self, faces):
        """Aggiunge multiple facce da lavorare (compatibilità)."""
        for face in faces:
            self.add_face(face)

    @property
    def faces(self):
        """Proprietà per compatibilità con il vecchio API."""
        return self.surfaces

    def set_speeds_and_feeds(self, spindle_speed: int, feed_rate: float, plunge_rate: float = None):
        """
        Imposta velocità mandrino e avanzamenti.

        Args:
            spindle_speed: Velocità mandrino in RPM
            feed_rate: Velocità di avanzamento in mm/min
            plunge_rate: Velocità di affondamento in mm/min (opzionale)
        """
        self.spindle_speed = spindle_speed
        self.feed_rate = feed_rate
        if plunge_rate is not None:
            self.plunge_rate = plunge_rate

    def set_heights(self, safe_height: float, clearance_height: float = None):
        """
        Imposta altezze di sicurezza.

        Args:
            safe_height: Altezza di sicurezza in mm
            clearance_height: Altezza di sgombero in mm (opzionale)
        """
        self.safe_height = safe_height
        if clearance_height is not None:
            self.clearance_height = clearance_height

    def set_precision(self, tolerance: float, step_down: float = None, step_over: float = None):
        """
        Imposta parametri di precisione.

        Args:
            tolerance: Tolleranza di lavorazione in mm
            step_down: Profondità di passata in mm (opzionale)
            step_over: Passo laterale in mm (opzionale)
        """
        self.tolerance = tolerance
        if step_down is not None:
            self.step_down = step_down
        if step_over is not None:
            self.step_over = step_over

    @abstractmethod
    def validate(self) -> Tuple[bool, List[str]]:
        """
        Valida la configurazione della lavorazione.

        Returns:
            Tuple con (is_valid, error_messages)
        """
        pass

    @abstractmethod
    def calculate_machining_time(self) -> float:
        """
        Calcola il tempo stimato di lavorazione.

        Returns:
            Tempo in minuti
        """
        pass

    def get_parameters(self) -> Dict[str, Any]:
        """Restituisce tutti i parametri della lavorazione."""
        return {
            'name': self.name,
            'type': self.operation_type.value,
            'tool': self.tool.name if self.tool else None,
            'spindle_speed': self.spindle_speed,
            'feed_rate': self.feed_rate,
            'plunge_rate': self.plunge_rate,
            'safe_height': self.safe_height,
            'clearance_height': self.clearance_height,
            'cutting_direction': self.cutting_direction.value,
            'tolerance': self.tolerance,
            'step_down': self.step_down,
            'step_over': self.step_over,
            'enabled': self.enabled
        }


class RoughingOperation(Operation):
    """Lavorazione di sgrossatura."""

    def __init__(self, name: str):
        super().__init__(name, OperationType.ROUGHING)

        # Parametri specifici per sgrossatura
        self.stock_to_leave = 0.5    # mm di sovrametallo
        self.max_depth = 5.0         # mm profondità massima per passata
        self.roughing_strategy = "zigzag"  # "zigzag", "spiral", "parallel"

    def set_roughing_parameters(self, stock_to_leave: float, max_depth: float, strategy: str = "zigzag"):
        """
        Imposta parametri specifici per la sgrossatura.

        Args:
            stock_to_leave: Sovrametallo da lasciare in mm
            max_depth: Profondità massima per passata in mm
            strategy: Strategia di sgrossatura
        """
        self.stock_to_leave = stock_to_leave
        self.max_depth = max_depth
        self.roughing_strategy = strategy

    def validate(self) -> Tuple[bool, List[str]]:
        """Valida la configurazione della sgrossatura."""
        errors = []

        if not self.tool:
            errors.append("Nessun utensile selezionato")

        if not self.surfaces:
            errors.append("Nessuna superficie selezionata")

        if self.stock_to_leave < 0:
            errors.append("Il sovrametallo non può essere negativo")

        if self.max_depth <= 0:
            errors.append("La profondità massima deve essere positiva")

        return len(errors) == 0, errors

    def calculate_machining_time(self) -> float:
        """Calcola il tempo stimato per la sgrossatura."""
        if not self.tool or not self.surfaces:
            return 0.0

        # Calcolo basato su area delle superfici da lavorare
        total_area = sum(surface.get_area() for surface in self.surfaces)
        removal_rate = self.feed_rate * self.tool.diameter * self.step_down  # mm³/min
        estimated_volume = total_area * self.max_depth  # mm³ stimato

        return estimated_volume / removal_rate if removal_rate > 0 else 0.0


class ContouringOperation(Operation):
    """Lavorazione di contornatura/finitura."""

    def __init__(self, name: str):
        super().__init__(name, OperationType.CONTOURING)

        # Parametri specifici per contornatura
        self.finish_passes = 1       # Numero di passate di finitura
        self.spring_passes = 0       # Passate a vuoto per finitura
        self.lead_in_distance = 2.0  # mm di entrata graduale
        self.lead_out_distance = 2.0 # mm di uscita graduale

    def set_contouring_parameters(self, finish_passes: int = 1, spring_passes: int = 0,
                                 lead_in: float = 2.0, lead_out: float = 2.0):
        """
        Imposta parametri specifici per la contornatura.

        Args:
            finish_passes: Numero di passate di finitura
            spring_passes: Passate a vuoto
            lead_in: Distanza di entrata graduale
            lead_out: Distanza di uscita graduale
        """
        self.finish_passes = finish_passes
        self.spring_passes = spring_passes
        self.lead_in_distance = lead_in
        self.lead_out_distance = lead_out

    def validate(self) -> Tuple[bool, List[str]]:
        """Valida la configurazione della contornatura."""
        errors = []

        if not self.tool:
            errors.append("Nessun utensile selezionato")

        if not self.surfaces:
            errors.append("Nessuna superficie selezionata")

        if self.finish_passes < 1:
            errors.append("Deve esserci almeno una passata di finitura")

        return len(errors) == 0, errors

    def calculate_machining_time(self) -> float:
        """Calcola il tempo stimato per la contornatura."""
        if not self.tool or not self.surfaces:
            return 0.0

        # Calcolo basato su perimetro delle superfici
        total_perimeter = 0
        for surface in self.surfaces:
            bbox = surface.get_bounding_box()
            width = bbox[1][0] - bbox[0][0]
            height = bbox[1][1] - bbox[0][1]
            total_perimeter += 2 * (width + height)  # Perimetro approssimativo

        total_passes = self.finish_passes + self.spring_passes
        return (total_perimeter * total_passes) / self.feed_rate


class EngravingOperation(Operation):
    """Lavorazione di incisione."""

    def __init__(self, name: str):
        super().__init__(name, OperationType.ENGRAVING)

        # Parametri specifici per incisione
        self.engraving_depth = 0.1   # mm profondità incisione
        self.text_content = ""       # Testo da incidere
        self.font_size = 10.0        # mm altezza caratteri
        self.line_spacing = 1.2      # Spaziatura tra righe

    def set_engraving_parameters(self, depth: float, text: str = "", font_size: float = 10.0):
        """
        Imposta parametri specifici per l'incisione.

        Args:
            depth: Profondità di incisione in mm
            text: Testo da incidere
            font_size: Dimensione del font in mm
        """
        self.engraving_depth = depth
        self.text_content = text
        self.font_size = font_size

    def validate(self) -> Tuple[bool, List[str]]:
        """Valida la configurazione dell'incisione."""
        errors = []

        if not self.tool:
            errors.append("Nessun utensile selezionato")

        if self.engraving_depth <= 0:
            errors.append("La profondità di incisione deve essere positiva")

        if not self.text_content and not self.surfaces:
            errors.append("Specificare testo da incidere o selezionare superfici")

        return len(errors) == 0, errors

    def calculate_machining_time(self) -> float:
        """Calcola il tempo stimato per l'incisione."""
        if not self.tool:
            return 0.0

        # Calcolo basato su lunghezza del testo
        char_length = len(self.text_content) * self.font_size * 0.6  # mm stimati per carattere

        return char_length / self.feed_rate


class DrillingOperation(Operation):
    """Lavorazione di foratura."""

    def __init__(self, name: str):
        super().__init__(name, OperationType.DRILLING)

        # Parametri specifici per foratura
        self.hole_depth = 10.0       # mm profondità foro
        self.peck_depth = 2.0        # mm per ogni beccata
        self.dwell_time = 0.5        # secondi di sosta
        self.retract_height = 1.0    # mm di ritrazione tra beccate

    def set_drilling_parameters(self, hole_depth: float, peck_depth: float = 2.0,
                               dwell_time: float = 0.5, retract_height: float = 1.0):
        """
        Imposta parametri specifici per la foratura.

        Args:
            hole_depth: Profondità del foro
            peck_depth: Profondità per beccata
            dwell_time: Tempo di sosta in secondi
            retract_height: Altezza di ritrazione
        """
        self.hole_depth = hole_depth
        self.peck_depth = peck_depth
        self.dwell_time = dwell_time
        self.retract_height = retract_height

    def validate(self) -> Tuple[bool, List[str]]:
        """Valida la configurazione della foratura."""
        errors = []

        if not self.tool:
            errors.append("Nessun utensile selezionato")

        if self.hole_depth <= 0:
            errors.append("La profondità del foro deve essere positiva")

        if self.peck_depth <= 0:
            errors.append("La profondità di beccata deve essere positiva")

        return len(errors) == 0, errors

    def calculate_machining_time(self) -> float:
        """Calcola il tempo stimato per la foratura."""
        if not self.tool:
            return 0.0

        # Calcolo basato su numero di beccate e profondità
        num_pecks = int(self.hole_depth / self.peck_depth) + 1
        drilling_time = (self.hole_depth / self.plunge_rate) * num_pecks
        dwell_total = self.dwell_time * num_pecks / 60  # converti in minuti

        return drilling_time + dwell_total


def create_operation(operation_type: OperationType, name: str) -> Operation:
    """
    Factory function per creare lavorazioni.

    Args:
        operation_type: Tipo di lavorazione
        name: Nome della lavorazione

    Returns:
        Istanza della lavorazione appropriata
    """
    if operation_type == OperationType.ROUGHING:
        return RoughingOperation(name)
    elif operation_type == OperationType.CONTOURING:
        return ContouringOperation(name)
    elif operation_type == OperationType.ENGRAVING:
        return EngravingOperation(name)
    elif operation_type == OperationType.DRILLING:
        return DrillingOperation(name)
    else:
        raise ValueError(f"Tipo di lavorazione non supportato: {operation_type}")


def get_recommended_parameters(operation_type: OperationType, material: str, tool_diameter: float) -> Dict[str, Any]:
    """
    Restituisce parametri raccomandati per una lavorazione.

    Args:
        operation_type: Tipo di lavorazione
        material: Materiale da lavorare
        tool_diameter: Diametro utensile in mm

    Returns:
        Dizionario con parametri raccomandati
    """
    # Database semplificato di parametri raccomandati
    material_factors = {
        "Aluminum": {"speed_factor": 1.0, "feed_factor": 1.0},
        "Steel": {"speed_factor": 0.6, "feed_factor": 0.8},
        "Plastic": {"speed_factor": 1.5, "feed_factor": 1.2},
        "Wood": {"speed_factor": 2.0, "feed_factor": 1.5}
    }

    base_params = {
        OperationType.ROUGHING: {"spindle_speed": 2000, "feed_rate": 200},
        OperationType.CONTOURING: {"spindle_speed": 3000, "feed_rate": 150},
        OperationType.ENGRAVING: {"spindle_speed": 5000, "feed_rate": 100},
        OperationType.DRILLING: {"spindle_speed": 1500, "feed_rate": 50}
    }

    if operation_type not in base_params:
        return {}

    params = base_params[operation_type].copy()

    # Applica fattori del materiale
    if material in material_factors:
        factors = material_factors[material]
        params["spindle_speed"] = int(params["spindle_speed"] * factors["speed_factor"])
        params["feed_rate"] = int(params["feed_rate"] * factors["feed_factor"])

    # Aggiusta per diametro utensile
    diameter_factor = max(0.5, min(2.0, tool_diameter / 6.0))  # Normalizza intorno a 6mm
    params["feed_rate"] = int(params["feed_rate"] * diameter_factor)

    return params
