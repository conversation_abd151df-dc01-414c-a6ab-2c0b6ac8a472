# tests/test_axis_coordinate_rough.py
import pytest

from CAM.setup import AxisConfiguration, CoordinateMapper, Rough, setup

class TestAxisConfiguration:
    @pytest.mark.parametrize("name,inverted,mapped_to,expected_str", [
        ('X', False, None, 'Asse X: normale'),
        ('Y', True, None, 'Asse Y: invertito'),
        ('A', False, 'B', 'Asse A (mappato a B): normale'),
        ('C', True, 'Z', 'Asse C (mappato a Z): invertito'),
    ])
    def test_str_and_mapping(self, name, inverted, mapped_to, expected_str):
        cfg = AxisConfiguration(name, inverted, mapped_to)
        assert str(cfg) == expected_str
        assert cfg.mapped_to == (mapped_to or name)

    def test_transform_value(self):
        cfg_normal = AxisConfiguration('X', False)
        cfg_inverted = AxisConfiguration('X', True)
        assert cfg_normal.transform_value(5.0) == 5.0
        assert cfg_inverted.transform_value(5.0) == -5.0

class TestCoordinateMapper:
    def test_default_mapping(self):
        mapper = CoordinateMapper()
        result = mapper.transform_point(1.0, 2.0, 3.0)
        assert result == {'X': 1.0, 'Y': 2.0, 'Z': 3.0}

    def test_custom_axis_config(self):
        mapper = CoordinateMapper()
        mapper.set_axis_config('X', inverted=True)
        result = mapper.transform_point(1.0, 2.0, 3.0)
        assert result['X'] == -1.0
        assert result['Y'] == 2.0
        assert result['Z'] == 3.0

    def test_axis_mapping(self):
        mapper = CoordinateMapper()
        mapper.set_axis_config('U', mapped_to='Y')
        result = mapper.transform_point(1.0, 2.0, 3.0)
        assert result['U'] == 2.0

    def test_add_rotary_axis(self):
        mapper = CoordinateMapper()
        mapper.add_rotary_axis('A', rotation_around='Z', inverted=True)
        result = mapper.transform_point(1.0, 2.0, 3.0, rotary_value=45.0)
        assert result['A'] == -45.0
        assert result['X'] == 1.0
        
        # Verifica configurazione rotativa
        assert mapper.rotary_axis == {'name': 'A', 'rotation_around': 'Z', 'inverted': True}

    def test_add_rotary_axis_invalid_rotation(self):
        mapper = CoordinateMapper()
        with pytest.raises(ValueError):
            mapper.add_rotary_axis('B', rotation_around='W')

    def test_multiple_rotary_axis_error(self):
        mapper = CoordinateMapper()
        mapper.add_rotary_axis('A', rotation_around='X')
        with pytest.raises(ValueError):
            mapper.add_rotary_axis('B', rotation_around='Y')

    def test_str_representation(self):
        mapper = CoordinateMapper()
        mapper.set_axis_config('Y', inverted=True)
        mapper.add_rotary_axis('C', rotation_around='Z')
        text = str(mapper)
        
        assert 'Asse Y: invertito' in text
        assert 'Asse C attorno a Z: normale' in text
        assert 'Configurazione assi:' in text

class TestRough:
    def test_cuboid_bbox(self):
        rough = Rough('cuboid', dims=(10.0, 20.0, 30.0), offsets={'X': (1,2), 'Y': (0,0), 'Z': (3,4)})
        assert rough.bbox == {
            'X': (-1.0, 10.0+2),
            'Y': (-0.0, 20.0+0),
            'Z': (-3.0, 30.0+4),
        }

    @pytest.mark.parametrize("axis,expected", [
        ('X', {'X': (40.0, 60.0), 'Y': (0.0, 5.0), 'Z': (0.0, 5.0)}),
        ('Y', {'X': (0.0, 5.0), 'Y': (40.0, 60.0), 'Z': (0.0, 5.0)}),
        ('Z', {'X': (0.0, 5.0), 'Y': (0.0, 5.0), 'Z': (40.0, 60.0)}),
    ])
    def test_cylinder_bbox(self, axis, expected):
        # dims: (diameter, height) = (10, 20) so offset default (0,0)
        rough = Rough('cylinder', dims=(10.0, 20.0), cylinder_axis=axis)
        # With default offsets, bbox directly base => (0, base)
        base = expected
        assert rough.bbox == base

    def test_model_bbox(self):
        rough = Rough('model', model_bbox=(7.0,8.0,9.0))
        assert rough.bbox == {'X': (0.0,7.0), 'Y': (0.0,8.0), 'Z': (0.0,9.0)}

    def test_invalid_parameters(self):
        with pytest.raises(ValueError):
            Rough('cuboid')  # missing dims
        with pytest.raises(ValueError):
            Rough('cylinder', dims=(5.0,))  # incorrect dims length

class TestSetup:
    @pytest.mark.parametrize("shape,params,expected_error", [
        ('cuboid', {}, ValueError),
        ('cylinder', {'dims': (5.0,)}, ValueError),
        ('model', {'model_bbox': ()}, TypeError),
        ('sphere', {'dims': (10,)}, ValueError),
    ])
    def test_invalid_parameters(self, shape, params, expected_error):
        with pytest.raises(expected_error):
            setup(shape, **params)

    def test_valid_setup_with_offsets(self):
        rough = setup('cuboid', dims=(10,20,30), offsets={'X': (1,2)})
        assert rough.bbox['X'] == (-1.0, 10.0+2)
        assert isinstance(rough, Rough)

    def test_error_messages(self):
        with pytest.raises(ValueError) as excinfo:
            setup('invalid_shape')
        assert "Forma non supportata" in str(excinfo.value)

    def test_cylinder_axis_validation(self):
        with pytest.raises(ValueError):
            setup('cylinder', dims=(10,20), cylinder_axis='invalid')
