"""
Test script per le operazioni CAM con visualizzazione PyVista.

Questo script testa:
1. Creazione di operazioni (sgrossatura, contornatura, incisione)
2. Configurazione parametri
3. Validazione operazioni
4. Visualizzazione superfici e parametri
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pyvista as pv
import numpy as np
from operations import (
    OperationType, RoughingOperation, ContouringOperation, EngravingOperation,
    MathematicalSurface, get_recommended_parameters
)


# Classe placeholder per Tool se non disponibile
class Tool:
    def __init__(self, name, diameter, length, code=None):
        self.name = name
        self.diameter = diameter
        self.length = length
        self.code = code


def create_surface_mesh(surface):
    """Crea una mesh PyVista da una superficie matematica."""
    if surface.surface_type == 'plane':
        center = surface.parameters.get('center', (0, 0, 0))
        size = surface.parameters.get('size', (10, 10))
        
        # Crea piano
        plane = pv.Plane(
            center=center,
            direction=(0, 0, 1),
            i_size=size[0],
            j_size=size[1]
        )
        return plane
        
    elif surface.surface_type == 'cylinder':
        center = surface.parameters.get('center', (0, 0, 0))
        radius = surface.parameters.get('radius', 1)
        height = surface.parameters.get('height', 1)
        
        # Crea cilindro
        cylinder = pv.Cylinder(
            center=(center[0], center[1], center[2] + height/2),
            direction=(0, 0, 1),
            radius=radius,
            height=height
        )
        return cylinder
        
    else:
        # Superficie custom - usa bounding box
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox
        
        box = pv.Box(bounds=[
            min_point[0], max_point[0],
            min_point[1], max_point[1], 
            min_point[2], max_point[2]
        ])
        return box


def test_roughing_operation():
    """Test operazione di sgrossatura."""
    print("=== Test Operazione di Sgrossatura ===")
    
    # Crea superficie da sgrossare
    surface = MathematicalSurface('plane', {
        'center': (0, 0, 0),
        'size': (50, 30),
        'normal': (0, 0, 1)
    })
    
    # Crea utensile
    tool = Tool("Fresa_Sgrossatura_8mm", diameter=8.0, length=50.0, code="T1")
    
    # Crea operazione
    operation = RoughingOperation("Sgrossatura_Test")
    operation.set_tool(tool)
    operation.add_surface(surface)
    
    # Configura parametri
    operation.set_roughing_parameters(
        stock_to_leave=0.5,
        max_depth=2.0,
        strategy="zigzag"
    )
    
    # Parametri raccomandati
    try:
        recommended = get_recommended_parameters(OperationType.ROUGHING, "Aluminum", 8.0)
        operation.set_speeds_and_feeds(
            spindle_speed=recommended["spindle_speed"],
            feed_rate=recommended["feed_rate"],
            plunge_rate=50
        )
    except:
        # Valori di default se get_recommended_parameters non è disponibile
        operation.set_speeds_and_feeds(
            spindle_speed=2000,
            feed_rate=300,
            plunge_rate=50
        )
    
    operation.set_precision(tolerance=0.1, step_down=2.0, step_over=6.0)
    operation.set_heights(safe_height=10.0, clearance_height=3.0)
    
    # Test validazione
    is_valid, errors = operation.validate()
    print(f"Operazione valida: {is_valid}")
    if not is_valid:
        print(f"Errori: {errors}")
    
    # Test calcolo tempo
    machining_time = operation.calculate_machining_time()
    print(f"Tempo stimato: {machining_time:.2f} minuti")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Operazione di Sgrossatura")
    
    # Superficie da lavorare
    surface_mesh = create_surface_mesh(surface)
    plotter.add_mesh(surface_mesh, color='lightblue', opacity=0.7, label='Superficie da Sgrossare')
    
    # Visualizza parametri come testo
    info_text = f"""Operazione: {operation.name}
Utensile: {tool.name} (Ø{tool.diameter}mm)
Strategia: {operation.roughing_strategy}
Sovrametallo: {operation.stock_to_leave}mm
Profondità max: {operation.max_depth}mm
Velocità mandrino: {operation.spindle_speed} RPM
Avanzamento: {operation.feed_rate} mm/min
Tempo stimato: {machining_time:.2f} min"""
    
    plotter.add_text(info_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return operation


def test_contouring_operation():
    """Test operazione di contornatura."""
    print("\n=== Test Operazione di Contornatura ===")
    
    # Crea superficie da contornare
    surface = MathematicalSurface('plane', {
        'center': (25, 15, 5),
        'size': (40, 25),
        'normal': (0, 0, 1)
    })
    
    # Crea utensile
    tool = Tool("Fresa_Finitura_4mm", diameter=4.0, length=40.0, code="T2")
    
    # Crea operazione
    operation = ContouringOperation("Contornatura_Test")
    operation.set_tool(tool)
    operation.add_surface(surface)
    
    # Configura parametri
    operation.set_contouring_parameters(
        finish_passes=2,
        spring_passes=1,
        lead_in=3.0,
        lead_out=3.0
    )
    
    # Parametri di taglio
    try:
        recommended = get_recommended_parameters(OperationType.CONTOURING, "Steel", 4.0)
        operation.set_speeds_and_feeds(
            spindle_speed=recommended["spindle_speed"],
            feed_rate=recommended["feed_rate"],
            plunge_rate=30
        )
    except:
        operation.set_speeds_and_feeds(
            spindle_speed=3000,
            feed_rate=200,
            plunge_rate=30
        )
    
    operation.set_precision(tolerance=0.02, step_down=0.5)
    operation.set_heights(safe_height=8.0, clearance_height=2.0)
    
    # Test validazione
    is_valid, errors = operation.validate()
    print(f"Operazione valida: {is_valid}")
    if not is_valid:
        print(f"Errori: {errors}")
    
    # Test calcolo tempo
    machining_time = operation.calculate_machining_time()
    print(f"Tempo stimato: {machining_time:.2f} minuti")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Operazione di Contornatura")
    
    # Superficie da contornare
    surface_mesh = create_surface_mesh(surface)
    plotter.add_mesh(surface_mesh, color='lightgreen', opacity=0.7, label='Superficie da Contornare')
    
    # Simula percorso di contornatura (perimetro)
    bbox = surface.get_bounding_box()
    min_pt, max_pt = bbox
    
    # Punti del perimetro
    perimeter_points = [
        [min_pt[0], min_pt[1], max_pt[2] + 1],
        [max_pt[0], min_pt[1], max_pt[2] + 1],
        [max_pt[0], max_pt[1], max_pt[2] + 1],
        [min_pt[0], max_pt[1], max_pt[2] + 1],
        [min_pt[0], min_pt[1], max_pt[2] + 1]  # Chiude il loop
    ]
    
    # Crea linea del percorso
    perimeter_line = pv.Spline(np.array(perimeter_points))
    plotter.add_mesh(perimeter_line, color='red', line_width=3, label='Percorso Contornatura')
    
    # Informazioni operazione
    info_text = f"""Operazione: {operation.name}
Utensile: {tool.name} (Ø{tool.diameter}mm)
Passate finitura: {operation.finish_passes}
Passate a vuoto: {operation.spring_passes}
Lead-in/out: {operation.lead_in_distance}mm
Velocità mandrino: {operation.spindle_speed} RPM
Avanzamento: {operation.feed_rate} mm/min
Tempo stimato: {machining_time:.2f} min"""
    
    plotter.add_text(info_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return operation


def test_engraving_operation():
    """Test operazione di incisione."""
    print("\n=== Test Operazione di Incisione ===")
    
    # Crea superficie cilindrica per incisione
    surface = MathematicalSurface('cylinder', {
        'center': (0, 0, 0),
        'radius': 20,
        'height': 50
    })
    
    # Crea utensile
    tool = Tool("Fresa_Incisione_1mm", diameter=1.0, length=30.0, code="T3")
    
    # Crea operazione
    operation = EngravingOperation("Incisione_Test")
    operation.set_tool(tool)
    operation.add_surface(surface)
    
    # Configura parametri
    operation.set_engraving_parameters(
        depth=0.2,
        text="CAM LIB",
        font_size=8.0
    )
    
    # Parametri di taglio
    operation.set_speeds_and_feeds(
        spindle_speed=5000,
        feed_rate=100,
        plunge_rate=25
    )
    
    operation.set_precision(tolerance=0.01)
    operation.set_heights(safe_height=5.0, clearance_height=1.0)
    
    # Test validazione
    is_valid, errors = operation.validate()
    print(f"Operazione valida: {is_valid}")
    if not is_valid:
        print(f"Errori: {errors}")
    
    # Visualizzazione
    plotter = pv.Plotter(title="Operazione di Incisione")
    
    # Superficie cilindrica
    surface_mesh = create_surface_mesh(surface)
    plotter.add_mesh(surface_mesh, color='lightyellow', opacity=0.7, label='Superficie per Incisione')
    
    # Simula pattern di incisione (linee orizzontali)
    center = surface.parameters['center']
    radius = surface.parameters['radius']
    height = surface.parameters['height']
    
    # Linee di incisione
    num_lines = 5
    for i in range(num_lines):
        y = center[1] - height/4 + (i / (num_lines-1)) * height/2
        z = center[2] + height/2 - operation.engraving_depth
        
        line_points = [
            [center[0] - radius*0.8, y, z],
            [center[0] + radius*0.8, y, z]
        ]
        
        line = pv.Line(line_points[0], line_points[1])
        plotter.add_mesh(line, color='red', line_width=2)
    
    # Informazioni operazione
    info_text = f"""Operazione: {operation.name}
Utensile: {tool.name} (Ø{tool.diameter}mm)
Testo: {operation.text_content}
Profondità: {operation.engraving_depth}mm
Dimensione font: {operation.font_size}mm
Velocità mandrino: {operation.spindle_speed} RPM
Avanzamento: {operation.feed_rate} mm/min"""
    
    plotter.add_text(info_text, position='upper_left', font_size=10)
    
    plotter.add_legend()
    plotter.show_axes()
    plotter.show()
    
    return operation


def main():
    """Esegue tutti i test."""
    print("Test Operazioni CAM con Visualizzazione PyVista")
    print("=" * 60)
    
    try:
        # Test 1: Sgrossatura
        roughing_op = test_roughing_operation()
        
        # Test 2: Contornatura
        contouring_op = test_contouring_operation()
        
        # Test 3: Incisione
        engraving_op = test_engraving_operation()
        
        print("\n" + "=" * 60)
        print("Tutti i test completati con successo!")
        
        # Riepilogo operazioni
        print("\nOperazioni testate:")
        operations = [roughing_op, contouring_op, engraving_op]
        for op in operations:
            print(f"  {op.name}: {op.operation_type.value} con {op.tool.name}")
            
    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
